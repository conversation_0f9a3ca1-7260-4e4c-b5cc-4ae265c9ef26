import axios from 'axios';

class BnbService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1 minute cache
    this.lastKnownPrice = 600; // Fallback price
    this.address = '0xDcE42A2ea35C9fDdfea7Af2D7798931CB12170A2';
    this.apiBase = 'https://api.bscscan.com/api';
  }

  // Cache management
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  // Get BNB price in USD
  async getBnbPrice() {
    const cacheKey = 'bnb_price';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // Using CoinGecko for price (free API)
      const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=binancecoin&vs_currencies=usd');
      const price = response.data.binancecoin.usd;

      // Update last known price for fallback
      this.lastKnownPrice = price;

      this.setCachedData(cacheKey, price);
      return price;
    } catch (error) {
      console.error('Error fetching BNB price:', error);
      console.log(`Using fallback price: $${this.lastKnownPrice}`);
      // Return last known price instead of hardcoded fallback
      return this.lastKnownPrice;
    }
  }

  // Convert USD to BNB
  async convertUsdToBnb(usdAmount) {
    try {
      const bnbPrice = await this.getBnbPrice();
      const bnbAmount = usdAmount / bnbPrice;
      
      return {
        bnbAmount: bnbAmount,
        bnbPrice: bnbPrice,
        formattedAmount: bnbAmount.toFixed(6)
      };
    } catch (error) {
      console.error('Error converting USD to BNB:', error);
      throw error;
    }
  }

  // Calculate USD value of BNB amount at time of transaction
  async calculateUsdValue(bnbAmount, txDate) {
    try {
      // For real-time transactions, use current price
      const now = new Date();
      const txTime = new Date(txDate);
      const timeDiff = now - txTime;
      
      // If transaction is less than 1 hour old, use current price
      if (timeDiff < 3600000) {
        const currentPrice = await this.getBnbPrice();
        return bnbAmount * currentPrice;
      }
      
      // For older transactions, you might want to use historical price APIs
      // For now, we'll use current price as approximation
      const currentPrice = await this.getBnbPrice();
      return bnbAmount * currentPrice;
    } catch (error) {
      console.error('Error calculating USD value:', error);
      return 0;
    }
  }

  // Format BNB amount for display
  formatAmount(amount) {
    return parseFloat(amount).toFixed(6);
  }

  // Get address balance and info (using free BSCScan API)
  async getAddressInfo() {
    const cacheKey = `bnb_address_${this.address}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // Get balance (using free API without key for demo)
      const balanceResponse = await axios.get(`${this.apiBase}?module=account&action=balance&address=${this.address}&tag=latest`);

      console.log('BNB API Response:', balanceResponse.data);

      if (balanceResponse.data.status !== '1') {
        console.error('BNB API Error:', balanceResponse.data.message);
        throw new Error(balanceResponse.data.message || 'API request failed');
      }

      const balance = parseFloat(balanceResponse.data.result) / Math.pow(10, 18); // Convert Wei to BNB

      // Get transaction count
      const txCountResponse = await axios.get(`${this.apiBase}?module=proxy&action=eth_getTransactionCount&address=${this.address}&tag=latest`);
      const txCount = parseInt(txCountResponse.data.result, 16);

      const data = {
        balance: isNaN(balance) ? 0 : balance,
        unconfirmed_balance: 0, // BNB Smart Chain doesn't have unconfirmed balance concept like Bitcoin
        total_received: isNaN(balance) ? 0 : balance, // Simplified - would need to calculate from all transactions
        total_sent: 0, // Simplified - would need to calculate from all transactions
        n_tx: isNaN(txCount) ? 0 : txCount,
        unconfirmed_n_tx: 0
      };

      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching BNB address info:', error);
      // Return fallback data with demo values for testing
      console.log('Using fallback BNB data due to API error');
      return {
        balance: 0.0,
        unconfirmed_balance: 0,
        total_received: 0.0,
        total_sent: 0,
        n_tx: 0,
        unconfirmed_n_tx: 0
      };
    }
  }

  // Get incoming transactions
  async getIncomingTransactions(limit = 10) {
    const cacheKey = `bnb_incoming_${this.address}_${limit}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await axios.get(`${this.apiBase}?module=account&action=txlist&address=${this.address}&startblock=0&endblock=********&page=1&offset=${limit}&sort=desc`);

      if (response.data.status !== '1') {
        return [];
      }

      const transactions = response.data.result
        .filter(tx => tx.to.toLowerCase() === this.address.toLowerCase()) // Only incoming
        .map(tx => ({
          hash: tx.hash,
          block_height: parseInt(tx.blockNumber),
          received: new Date(parseInt(tx.timeStamp) * 1000),
          confirmed: new Date(parseInt(tx.timeStamp) * 1000),
          confirmations: tx.confirmations ? parseInt(tx.confirmations) : 0,
          value: parseFloat(tx.value) / Math.pow(10, 18), // Convert Wei to BNB
          from: tx.from,
          to: tx.to,
          gas_used: parseInt(tx.gasUsed),
          gas_price: parseInt(tx.gasPrice)
        }));

      this.setCachedData(cacheKey, transactions);
      return transactions;
    } catch (error) {
      console.error('Error fetching BNB transactions:', error);
      return [];
    }
  }

  // Validate BNB address format (same as Ethereum)
  isValidAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }
}

export default new BnbService();
