* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
  background-color: #0a0a0a;
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem 2rem;
  width: auto;
  max-width: 800px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-scrolled {
  transform: translateX(-50%) scale(0.9);
  background-color: rgba(26, 26, 26, 0.7);
  backdrop-filter: blur(15px);
  border-color: rgba(255, 255, 255, 0.05);
  padding: 0.4rem 1.8rem;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-scrolled .nav-container {
  gap: 2.5rem;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-scrolled .nav-logo {
  font-size: 1.1rem;
  gap: 0.6rem;
}

.nav-logo-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-scrolled .nav-logo-image {
  width: 20px;
  height: 20px;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links a {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.nav-links a:hover {
  color: #ffffff;
}

.nav-auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  white-space: nowrap;
  flex-shrink: 0;
}

.nav-signin {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: 16px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  letter-spacing: 0.3px;
  white-space: nowrap;
  display: inline-block;
}

.nav-signin:hover {
  color: white;
  background: rgba(255, 255, 255, 0.08);
}

/* Force sign in button to stay on one line */
.nav-auth-buttons .nav-signin {
  font-size: 0.9rem !important;
  white-space: nowrap !important;
  display: inline-block !important;
  line-height: 1.2 !important;
}

.nav-cta {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 0.5rem 1.25rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.navbar-scrolled .nav-cta {
  padding: 0.4rem 1rem;
  font-size: 0.85rem;
}

.navbar-scrolled .nav-signin {
  padding: 0.3rem 0.6rem;
  font-size: 0.85rem;
}

.nav-cta:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

/* Dropdown Styles */
.nav-dropdown {
  position: relative;
}

.nav-dropdown-trigger {
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  vertical-align: baseline;
}

.nav-dropdown-trigger:hover {
  color: #ffffff;
}

.nav-dropdown-arrow {
  transition: transform 0.2s ease;
  width: 10px;
  height: 10px;
  flex-shrink: 0;
}

.nav-dropdown-arrow.open {
  transform: rotate(180deg);
}

.nav-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem 0;
  min-width: 180px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

/* Add invisible bridge to prevent dropdown from closing when moving cursor */
.nav-dropdown-menu::before {
  content: '';
  position: absolute;
  top: -0.5rem;
  left: 0;
  right: 0;
  height: 0.5rem;
  background: transparent;
}

.nav-dropdown-menu a {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.nav-dropdown-menu a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.nav-dropdown-submenu {
  position: relative;
}

.nav-dropdown-submenu-trigger {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-dropdown-submenu-trigger:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.nav-dropdown-arrow-right {
  transition: transform 0.2s ease;
}

.nav-dropdown-submenu-content {
  position: absolute;
  top: 0;
  left: 100%;
  margin-left: 0.5rem;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem 0;
  min-width: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1002;
}

/* Add invisible bridge for submenu */
.nav-dropdown-submenu-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -0.5rem;
  width: 0.5rem;
  height: 100%;
  background: transparent;
}

.nav-dropdown-submenu-content a {
  display: block;
  padding: 0.75rem 1rem;
  color: #a0a0a0;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.nav-dropdown-submenu-content a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

/* Mobile Navigation Styles */
.desktop-nav {
  display: flex;
}

.mobile-menu-button {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  padding: 6px;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-button:hover {
  background: rgba(0, 212, 170, 0.1);
  border-color: rgba(0, 212, 170, 0.3);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background-color: #ffffff;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  background: #0a0a0a;
  transform: translateX(-100%);
  opacity: 0;
  visibility: hidden;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 99999;
  overflow-y: auto;
  overflow-x: hidden;
  display: none; /* Hide by default on desktop */
  -webkit-overflow-scrolling: touch;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.mobile-nav.open {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

/* Prevent body scroll when mobile menu is open */
body.mobile-menu-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

.mobile-nav-close {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 300;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: none; /* Hidden by default on desktop */
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 100000;
  backdrop-filter: blur(10px);
}

.mobile-nav-close:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  transform: rotate(90deg);
}

.mobile-nav-content {
  padding: 4rem 1.5rem 4rem;
  min-height: 100vh;
  height: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 1.5rem;
  position: relative;
  box-sizing: border-box;
  overflow-y: visible;
  -webkit-overflow-scrolling: touch;
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  flex: 1;
}

.mobile-nav-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-nav-section h3 {
  color: #00d4aa;
  font-size: 0.85rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(0, 212, 170, 0.2);
}

/* Special styling for the first "Menu" header */
.mobile-nav-section:first-child h3 {
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0 0 2rem 0;
  padding: 1rem 0 1.5rem 0;
  text-align: center;
  border-bottom: 2px solid rgba(0, 212, 170, 0.3);
  letter-spacing: 2px;
}

.mobile-nav-section a {
  color: #ffffff;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1.25rem 1.5rem;
  margin: 0 -1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  position: relative;
}

.mobile-nav-section a:hover,
.mobile-nav-section a:active {
  color: #00d4aa;
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.05) 100%);
  border-left-color: #00d4aa;
  transform: translateX(5px);
  box-shadow: 0 2px 10px rgba(0, 212, 170, 0.1);
}

.mobile-nav-cta {
  margin-top: auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-cta-button {
  display: block;
  width: 100%;
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: #ffffff;
  padding: 1.25rem 1.5rem;
  text-decoration: none;
  border-radius: 16px;
  font-weight: 600;
  text-align: center;
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
}

.mobile-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 212, 170, 0.4);
  background: linear-gradient(135deg, #00e6bb 0%, #00c9a5 100%);
}

.mobile-nav-auth {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-signin-btn {
  display: block;
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.mobile-signin-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}



/* Hero Section */
.hero {
  padding: 10rem 0 4rem;
  text-align: center;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(0, 212, 170, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.hero-badge-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  max-width: 600px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.btn-secondary {
  color: #ffffff;
  padding: 1rem 2rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.btn-secondary:hover {
  color: #00d4aa;
}

.hero-image {
  margin-top: 4rem;
  position: relative;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Crypto Market Stats Section */
.crypto-market-stats {
  padding: 4rem 0;
  background-color: #0a0a0a;
  border-bottom: 1px solid #1a1a1a;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

/* Visual indicator for data updates */
.crypto-market-stats.data-updated {
  background-color: rgba(0, 255, 0, 0.05);
  border-bottom-color: rgba(0, 255, 0, 0.2);
}
}

.crypto-market-container {
  width: 100%;
  position: relative;
}

.crypto-market-track {
  display: flex;
  align-items: center;
  gap: 3rem;
  animation: scroll-crypto 25s linear infinite;
  width: max-content;
}

@keyframes scroll-crypto {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.crypto-stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  min-width: 200px;
  backdrop-filter: blur(10px);
}

.crypto-stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.1);
}

.crypto-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
}

.crypto-stat-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.crypto-stat-card:hover .crypto-stat-icon img {
  transform: scale(1.1);
}

.crypto-stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.crypto-stat-symbol {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.crypto-stat-price {
  font-size: 1rem;
  font-weight: 500;
  color: #e0e0e0;
  font-family: 'Monaco', 'Menlo', monospace;
}

.crypto-stat-change {
  font-size: 0.75rem;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
}

.crypto-stat-change.positive {
  color: #00d4aa;
}

.crypto-stat-change.negative {
  color: #ff6b6b;
}

.crypto-loading {
  text-align: center;
  color: #a0a0a0;
  font-size: 1rem;
  padding: 2rem;
}

/* Trust Badges Section */
.trust-badges {
  padding: 4rem 0;
  background-color: #0a0a0a;
  border-bottom: 1px solid #1a1a1a;
  overflow: hidden;
}

.trust-badges-container {
  width: 100%;
  position: relative;
}

.trust-badges-track {
  display: flex;
  align-items: center;
  gap: 6rem;
  animation: scroll-badges 15s linear infinite;
  width: max-content;
}

@keyframes scroll-badges {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  min-width: 200px;
  justify-content: center;
}

.trust-badge:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.trust-badge-icon {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.trust-badge-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter: grayscale(100%) brightness(0.6);
  transition: all 0.3s ease;
}

.trust-badge:hover .trust-badge-icon img {
  filter: grayscale(0%) brightness(1);
  transform: scale(1.1);
}

.trust-badge-name {
  color: #888;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.trust-badge:hover .trust-badge-name {
  color: #ffffff;
}

/* Features Section */
.features {
  padding: 6rem 0;
  background-color: #0a0a0a;
  position: relative;
}

.features-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
}

.features-header {
  text-align: left;
  margin-bottom: 4rem;
}

.features-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 4rem;
  align-items: flex-start;
}

.features-left {
  display: flex;
  flex-direction: column;
}

.features-title {
  font-size: 4.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.features-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  color: #666;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.feature-description {
  color: #888;
  line-height: 1.4;
  font-size: 0.875rem;
}

.features-right {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 1rem;
}

.dashboard-preview {
  width: 100%;
  max-width: 600px;
}

.dashboard-preview img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}


/* Testimonials Section */
.testimonials {
  padding: 6rem 0;
  background-color: #0a0a0a;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 3rem;
}

.testimonials-header {
  text-align: center;
  margin-bottom: 4rem;
}

.testimonials-title {
  font-size: 4rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.testimonials-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
}

.testimonials-scroll-container {
  width: 100%;
  position: relative;
}

.testimonials-track {
  display: flex;
  gap: 3rem;
  animation: scroll-testimonials 25s linear infinite;
  width: max-content;
}

@keyframes scroll-testimonials {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.testimonial-card {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  padding: 2.5rem;
  border-radius: 16px;
  border: 1px solid #333;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 400px;
  max-width: 400px;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.15);
  border-color: #00d4aa;
}

.testimonial-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #333;
  transition: border-color 0.3s ease;
}

.testimonial-card:hover .testimonial-avatar {
  border-color: #00d4aa;
}

.testimonial-author {
  flex: 1;
}

.testimonial-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.testimonial-role {
  font-size: 0.875rem;
  color: #00d4aa;
  font-weight: 500;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #e0e0e0;
  font-style: normal;
}

/* Stats Section */
.stats {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
  overflow: hidden;
}

.stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(0, 212, 170, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(79, 70, 229, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.stats-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  align-items: center;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 184, 148, 0.2));
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 16px;
  font-size: 1.5rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.3), rgba(0, 184, 148, 0.3));
  border-color: rgba(0, 212, 170, 0.5);
  transform: scale(1.1);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: 'Monaco', 'Menlo', monospace;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-number {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  color: #a0a0a0;
  font-weight: 500;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.stat-card:hover .stat-label {
  color: #e0e0e0;
}

/* Investment Plans Section */
.investment-plans {
  padding: 6rem 0;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
}

.investment-plans::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 212, 170, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.investment-plans-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.investment-plans-header {
  text-align: center;
  margin-bottom: 4rem;
}

.investment-plans-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.investment-plans-badge-icon {
  font-size: 1rem;
}

.investment-plans-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.investment-plans-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.investment-plans-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.investment-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.investment-plan-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
}

.investment-plan-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.investment-plan-card.popular {
  border-color: rgba(0, 212, 170, 0.5);
  background: rgba(0, 212, 170, 0.03);
}

.plan-popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto 1rem;
  transition: transform 0.3s ease;
}

.investment-plan-card:hover .plan-icon {
  transform: scale(1.1);
}

.plan-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-returns {
  margin-bottom: 0;
}

.plan-return-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.plan-return-label {
  font-size: 0.875rem;
  color: #a0a0a0;
  font-weight: 500;
}

.plan-return-value {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Monaco', 'Menlo', monospace;
}



/* Referral Program */
.referral-program {
  margin-top: 4rem;
}

.referral-program-content {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 20px;
  padding: 3rem;
  position: relative;
  overflow: hidden;
}

.referral-program-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 170, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.referral-program-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.referral-program-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  border-radius: 16px;
  color: #ffffff;
  flex-shrink: 0;
}

.referral-program-text {
  flex: 1;
}

.referral-program-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.referral-program-subtitle {
  font-size: 1rem;
  color: #a0a0a0;
}

.referral-commission {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 12px;
  min-width: 120px;
}

.referral-commission-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Monaco', 'Menlo', monospace;
}

.referral-commission-label {
  font-size: 0.75rem;
  color: #a0a0a0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.referral-program-description {
  font-size: 1.125rem;
  color: #e0e0e0;
  line-height: 1.6;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}



/* CTA Section */
.cta {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
  text-align: center;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-cta {
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.125rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: inline-block;
}

.btn-cta.primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
}

.btn-cta.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.btn-cta.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-cta.secondary:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Footer */
.footer {
  background-color: #111111;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 0 1rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 2rem;
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.footer-logo span {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
}

.footer-description {
  color: #a0a0a0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
  color: #a0a0a0;
}

.footer-social a:hover {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
  color: #00d4aa;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.footer-column h4 {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 0.5rem;
}

.footer-column ul li a {
  color: #a0a0a0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-column ul li a:hover {
  color: #00d4aa;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
}

.footer-bottom p {
  color: #666666;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-links {
    display: none;
  }

  .nav-dropdown-menu {
    right: -1rem;
    min-width: 200px;
  }

  .nav-dropdown-submenu-content {
    position: fixed;
    top: auto;
    left: 1rem;
    right: 1rem;
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .features-title {
    font-size: 2.5rem;
  }

  .features-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .features-header {
    text-align: center;
  }

  .testimonials-title {
    font-size: 2.5rem;
  }

  .testimonial-card {
    min-width: 300px;
    max-width: 300px;
    padding: 2rem;
  }

  .testimonials-track {
    gap: 2rem;
  }

  .crypto-stat-card {
    min-width: 180px;
    padding: 1.25rem 1.5rem;
  }

  .crypto-market-track {
    gap: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stat-card {
    padding: 2rem;
    gap: 1.25rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .investment-plans-title {
    font-size: 2.5rem;
  }

  .investment-plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .investment-plan-card {
    padding: 1.5rem;
  }

  .referral-program-content {
    padding: 2rem;
  }

  .referral-program-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footer-links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Mobile Navigation Responsive Styles */
@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .nav-auth-buttons {
    display: none;
  }

  .mobile-menu-button {
    display: flex;
  }

  .mobile-nav {
    display: block; /* Always present in DOM for proper transitions */
  }

  .mobile-nav.open {
    transform: translateX(0);
    opacity: 1;
    visibility: visible;
  }

  /* Mobile-specific menu improvements */
  .mobile-nav-close {
    display: flex; /* Use flex for better centering */
  }

  .mobile-nav-content {
    padding: 4rem 1.5rem 3rem; /* Account for close button and ensure bottom content is visible */
    min-height: 100vh;
    height: auto;
    overflow-y: visible;
  }

  /* Disable navbar scaling on mobile */
  .navbar-scrolled {
    transform: translateX(-50%) scale(1) !important;
    background-color: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-color: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 2rem;
  }

  .nav-container {
    padding: 1rem;
  }

  .nav-logo {
    font-size: 1.3rem;
  }

  .nav-logo-image {
    width: 28px;
    height: 28px;
  }

  /* Hero mobile adjustments */
  .hero {
    padding: 8rem 0 3rem;
  }

  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 280px;
    justify-content: center;
    padding: 1rem 2rem;
  }
}

@media (max-width: 480px) {
  .mobile-nav-content {
    padding: 4.5rem 1rem 3rem;
    min-height: 100vh;
    height: auto;
    overflow-y: visible;
  }

  .mobile-nav-close {
    top: 0.75rem;
    right: 0.75rem;
    width: 2.25rem;
    height: 2.25rem;
    font-size: 1.25rem;
  }

  .mobile-nav-section a {
    padding: 1rem 1.5rem;
    margin: 0 -0.5rem;
    font-size: 0.95rem;
  }

  .mobile-nav-section h3 {
    font-size: 0.8rem;
    margin-bottom: 0.75rem;
  }

  .mobile-cta-button {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  .mobile-signin-btn {
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
  }

  .nav-container {
    padding: 0.75rem;
  }

  .nav-logo {
    font-size: 1.2rem;
  }

  .nav-logo-image {
    width: 24px;
    height: 24px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 0.95rem;
  }
}

/* Comprehensive Mobile Responsive Styles */
@media (max-width: 768px) {
  /* Global mobile adjustments */
  .container {
    padding: 0 1rem;
  }

  /* Crypto Market Stats mobile */
  .crypto-market-stats {
    padding: 2rem 0;
  }

  .crypto-stat-card {
    min-width: 140px;
    padding: 0.75rem;
  }

  .crypto-stat-icon img {
    width: 20px;
    height: 20px;
  }

  .crypto-stat-symbol {
    font-size: 0.8rem;
  }

  .crypto-stat-price {
    font-size: 0.85rem;
  }

  .crypto-stat-change {
    font-size: 0.75rem;
  }

  /* Stats section mobile */
  .stats {
    padding: 3rem 0;
  }

  .stats-container {
    padding: 0 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }

  /* Features section mobile */
  .features {
    padding: 4rem 0;
  }

  .features-container {
    padding: 0 1rem;
  }

  .features-header h2 {
    font-size: 2.5rem;
  }

  .features-header p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }

  .feature-title {
    font-size: 1.3rem;
  }

  .feature-description {
    font-size: 0.95rem;
  }

  /* Investment Plans mobile */
  .investment-plans {
    padding: 4rem 0;
  }

  .investment-plans-container {
    padding: 0 1rem;
  }

  .investment-plans-header h2 {
    font-size: 2.5rem;
  }

  .investment-plans-header p {
    font-size: 1rem;
  }

  .investment-plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .investment-plan-card {
    padding: 2rem 1.5rem;
  }

  .plan-name {
    font-size: 1.3rem;
  }

  .plan-return {
    font-size: 2rem;
  }

  .plan-features li {
    font-size: 0.9rem;
  }

  /* Testimonials mobile */
  .testimonials {
    padding: 4rem 0;
  }

  .testimonials-container {
    padding: 0 1rem;
  }

  .testimonials-header h2 {
    font-size: 2.5rem;
  }

  .testimonials-header p {
    font-size: 1rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 2rem 1.5rem;
  }

  .testimonial-text {
    font-size: 0.95rem;
  }

  .testimonial-author h4 {
    font-size: 1rem;
  }

  .testimonial-author p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  /* Extra small mobile adjustments */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .features-header h2,
  .investment-plans-header h2,
  .testimonials-header h2 {
    font-size: 2rem;
  }

  .feature-card,
  .investment-plan-card,
  .testimonial-card {
    padding: 1.5rem 1rem;
  }

  .plan-return {
    font-size: 1.8rem;
  }
}
