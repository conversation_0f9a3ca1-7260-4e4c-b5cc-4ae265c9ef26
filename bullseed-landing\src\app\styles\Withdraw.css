/* Withdraw Page Styles */
.withdraw {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

.withdraw-header {
  margin-bottom: 32px;
  text-align: center;
}

.withdraw-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.withdraw-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.withdraw-content {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 32px;
  align-items: start;
}

.withdraw-main {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.withdraw-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Step Styles */
.withdraw-step {
  width: 100%;
}

.withdraw-step-header {
  margin-bottom: 32px;
  text-align: center;
}

.withdraw-step-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.withdraw-step-header p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.withdraw-balance-info {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 8px;
  color: #00d4aa;
  font-size: 14px;
}

.withdraw-back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.withdraw-back-btn:hover {
  color: #00d4aa;
}

/* Amount Step */
.withdraw-amount-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
}

.withdraw-amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0 20px;
  width: 100%;
  max-width: 400px;
  transition: border-color 0.2s ease;
}

.withdraw-amount-input-container:focus-within {
  border-color: #00d4aa;
}

.withdraw-currency-symbol {
  font-size: 24px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
}

.withdraw-amount-input {
  flex: 1;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  font-weight: 600;
  padding: 20px 0;
  text-align: center;
  outline: none;
}

.withdraw-amount-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.withdraw-currency-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  margin-left: 8px;
}

.withdraw-continue-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
}

.withdraw-continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.withdraw-continue-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.withdraw-quick-amounts {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.withdraw-quick-amounts span {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.withdraw-quick-amount-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.withdraw-quick-amount-btn:hover:not(:disabled) {
  background: rgba(0, 212, 170, 0.1);
  border-color: rgba(0, 212, 170, 0.3);
  color: #00d4aa;
}

.withdraw-quick-amount-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Crypto & Address Step */
.withdraw-crypto-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.withdraw-crypto-selection h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.withdraw-crypto-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.withdraw-crypto-option {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.withdraw-crypto-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.withdraw-crypto-option.selected {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
}

.withdraw-crypto-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
}

.withdraw-crypto-info {
  flex: 1;
}

.withdraw-crypto-info h4 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.withdraw-crypto-info span {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.withdraw-crypto-rate {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

/* Address Section */
.withdraw-address-section {
  margin-top: 24px;
}

.withdraw-address-section h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.withdraw-address-input-container {
  margin-bottom: 12px;
}

.withdraw-address-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 16px;
  padding: 16px 20px;
  outline: none;
  transition: border-color 0.2s ease;
}

.withdraw-address-input:focus {
  border-color: #00d4aa;
}

.withdraw-address-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.withdraw-address-note {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  line-height: 1.4;
}

.withdraw-address-note svg {
  flex-shrink: 0;
  margin-top: 2px;
  color: #ffa500;
}

/* Error Styles */
.withdraw-error {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  color: #ff6b6b;
  padding: 12px 16px;
  font-size: 14px;
  text-align: center;
}

/* Withdrawal History */
.withdraw-history {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.withdraw-history h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.withdraw-history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.withdraw-history-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.withdraw-history-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

.withdraw-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.withdraw-history-amount {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.withdraw-history-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.withdraw-history-status.pending {
  background: rgba(255, 165, 0, 0.2);
  color: #ffa500;
  border: 1px solid rgba(255, 165, 0, 0.3);
}

.withdraw-history-status.completed {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.withdraw-history-status.failed {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.withdraw-history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.withdraw-history-crypto {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

.withdraw-history-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.withdraw-history-address,
.withdraw-history-tx {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-family: 'Courier New', monospace;
  margin-top: 4px;
}

.withdraw-history-address span,
.withdraw-history-tx span {
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Support Section */
.withdraw-support {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
}

.withdraw-support h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.withdraw-support p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.withdraw-support-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.withdraw-support-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Confirmation Step */
.withdraw-confirmation-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.withdraw-confirmation-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.withdraw-confirmation-card h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}

.withdraw-confirmation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.withdraw-confirmation-row:last-child {
  border-bottom: none;
}

.withdraw-confirmation-row span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.withdraw-confirmation-row strong {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.withdraw-address-display {
  font-family: 'Courier New', monospace;
  font-size: 12px !important;
  word-break: break-all;
  max-width: 200px;
  text-align: right;
}

.withdraw-confirmation-actions {
  display: flex;
  justify-content: center;
}

.withdraw-confirm-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.withdraw-confirm-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.withdraw-confirm-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Success Step */
.withdraw-success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  text-align: center;
}

.withdraw-success-icon {
  width: 80px;
  height: 80px;
  background: rgba(0, 212, 170, 0.1);
  border: 2px solid #00d4aa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00d4aa;
}

.withdraw-success-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
}

.withdraw-success-details h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.withdraw-success-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.withdraw-success-row:last-child {
  border-bottom: none;
}

.withdraw-success-row span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.withdraw-success-row strong {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-pending {
  color: #ffa500 !important;
}

.withdraw-success-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 500px;
  text-align: left;
}

.withdraw-success-info h3 {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.withdraw-success-info ol {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.6;
  padding-left: 20px;
}

.withdraw-success-info li {
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .withdraw {
    padding: 16px;
  }

  .withdraw-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .withdraw-main {
    padding: 24px;
  }

  .withdraw-header h1 {
    font-size: 24px;
  }

  .withdraw-crypto-grid {
    grid-template-columns: 1fr;
  }

  .withdraw-crypto-option {
    padding: 16px;
  }

  .withdraw-amount-input-container {
    max-width: none;
  }

  .withdraw-quick-amounts {
    justify-content: flex-start;
  }

  .withdraw-address-display {
    max-width: 150px;
    font-size: 10px !important;
  }

  .withdraw-success-content {
    gap: 24px;
  }

  .withdraw-success-details,
  .withdraw-success-info {
    max-width: none;
  }
}
