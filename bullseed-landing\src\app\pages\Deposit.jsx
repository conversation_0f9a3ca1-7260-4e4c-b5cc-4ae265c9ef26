import React, { useState, useEffect, useRef } from 'react';
import cryptoService from '../../services/cryptoService.js';
import depositService from '../../services/depositService.js';
import paymentMonitorService from '../../services/paymentMonitorService.js';
import { supabase } from '../../lib/supabase.js';
import '../styles/Deposit.css';

const Deposit = ({ user }) => {
  const [step, setStep] = useState(1); // 1: Amount, 2: Crypto Selection, 3: Payment
  const [amount, setAmount] = useState('');
  const [selectedCrypto, setSelectedCrypto] = useState(null);
  const [supportedCryptos, setSupportedCryptos] = useState([]);
  const [currentDeposit, setCurrentDeposit] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [depositHistory, setDepositHistory] = useState([]);
  const [depositStatusUpdates, setDepositStatusUpdates] = useState({});
  const subscriptionRef = useRef(null);

  useEffect(() => {
    // Load supported cryptocurrencies
    const cryptos = cryptoService.getSupportedCryptocurrencies();
    setSupportedCryptos(cryptos);

    // Load deposit history
    loadDepositHistory();

    // Start payment monitoring
    paymentMonitorService.startMonitoring();

    // Set up real-time subscription for deposit updates
    if (user?.id) {
      setupDepositSubscription();
    }

    // Cleanup on unmount
    return () => {
      paymentMonitorService.stopMonitoring();
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [user]);

  const loadDepositHistory = async () => {
    if (user?.id) {
      try {
        const history = await depositService.getUserDeposits(user.id, 5);
        setDepositHistory(history);
      } catch (error) {
        console.error('Error loading deposit history:', error);
      }
    }
  };

  const setupDepositSubscription = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
    }

    subscriptionRef.current = supabase
      .channel('deposit_updates')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'crypto_deposits',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          handleDepositUpdate(payload);
        }
      )
      .subscribe();
  };

  const handleDepositUpdate = (payload) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    if (eventType === 'UPDATE' && newRecord) {
      // Update current deposit if it matches
      if (currentDeposit && currentDeposit.id === newRecord.id) {
        setCurrentDeposit(prev => ({
          ...prev,
          ...newRecord,
          crypto_details: prev.crypto_details,
          conversion_rate: prev.conversion_rate,
          formatted_amount: prev.formatted_amount
        }));
      }

      // Update deposit history
      setDepositHistory(prev =>
        prev.map(deposit =>
          deposit.id === newRecord.id ? newRecord : deposit
        )
      );

      // Show status update notification
      if (oldRecord && oldRecord.status !== newRecord.status) {
        setDepositStatusUpdates(prev => ({
          ...prev,
          [newRecord.id]: {
            oldStatus: oldRecord.status,
            newStatus: newRecord.status,
            timestamp: Date.now()
          }
        }));

        // Clear notification after 5 seconds
        setTimeout(() => {
          setDepositStatusUpdates(prev => {
            const updated = { ...prev };
            delete updated[newRecord.id];
            return updated;
          });
        }, 5000);
      }
    }

    if (eventType === 'INSERT' && newRecord) {
      // Add new deposit to history
      setDepositHistory(prev => [newRecord, ...prev.slice(0, 4)]);
    }
  };

  const handleAmountSubmit = (e) => {
    e.preventDefault();
    setError('');

    const amountNum = parseFloat(amount);
    if (!amountNum || amountNum < 200) {
      setError('Minimum deposit amount is $200');
      return;
    }

    if (amountNum > 100000) {
      setError('Maximum deposit amount is $100,000');
      return;
    }

    setStep(2);
  };

  const handleCryptoSelect = (crypto) => {
    setSelectedCrypto(crypto);
    setStep(3);
    createDepositRequest(crypto);
  };

  const createDepositRequest = async (crypto) => {
    setLoading(true);
    setError('');

    try {
      const deposit = await depositService.createDeposit(
        user.id,
        parseFloat(amount),
        crypto.id
      );

      setCurrentDeposit(deposit);
      loadDepositHistory(); // Refresh history
    } catch (error) {
      setError(error.message || 'Failed to create deposit request');
      setStep(2); // Go back to crypto selection
    } finally {
      setLoading(false);
    }
  };

  const resetDeposit = () => {
    setStep(1);
    setAmount('');
    setSelectedCrypto(null);
    setCurrentDeposit(null);
    setError('');
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
    });
  };

  const renderAmountStep = () => (
    <div className="deposit-step">
      <div className="deposit-step-header">
        <h2>Enter Deposit Amount</h2>
        <p>Minimum deposit: $200 • Maximum deposit: $100,000</p>
      </div>

      <form onSubmit={handleAmountSubmit} className="deposit-amount-form">
        <div className="deposit-amount-input-container">
          <span className="deposit-currency-symbol">$</span>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="200.00"
            min="200"
            max="100000"
            step="0.01"
            className="deposit-amount-input"
            required
          />
          <span className="deposit-currency-label">USD</span>
        </div>

        {error && <div className="deposit-error">{error}</div>}

        <button type="submit" className="deposit-continue-btn">
          Continue
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="9,18 15,12 9,6"/>
          </svg>
        </button>
      </form>

      <div className="deposit-quick-amounts">
        <span>Quick amounts:</span>
        {[200, 500, 1000, 5000].map(quickAmount => (
          <button
            key={quickAmount}
            onClick={() => setAmount(quickAmount.toString())}
            className="deposit-quick-amount-btn"
          >
            ${quickAmount}
          </button>
        ))}
      </div>
    </div>
  );

  const renderCryptoStep = () => {
    return (
      <div className="deposit-step">
        <div className="deposit-step-header">
          <button onClick={() => setStep(1)} className="deposit-back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15,18 9,12 15,6"/>
            </svg>
            Back
          </button>
          <h2>Select Payment Method</h2>
          <p>Choose your preferred cryptocurrency for your ${amount} deposit</p>
        </div>

        <div className="deposit-crypto-options">
          {supportedCryptos.map((crypto) => (
            <div
              key={crypto.id}
              className="deposit-crypto-option"
              onClick={() => handleCryptoSelect(crypto)}
            >
              <div className="deposit-crypto-icon" style={{ backgroundColor: crypto.color }}>
                {crypto.icon}
              </div>
              <div className="deposit-crypto-info">
                <div className="deposit-crypto-name">{crypto.name}</div>
                <div className="deposit-crypto-symbol">{crypto.symbol}</div>
                <div className="deposit-crypto-network">{crypto.network}</div>
              </div>
              <div className="deposit-crypto-arrow">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="9,18 15,12 9,6"/>
                </svg>
              </div>
            </div>
          ))}
        </div>

        {loading && (
          <div className="deposit-loading">
            Creating deposit request...
          </div>
        )}

        {error && <div className="deposit-error">{error}</div>}
      </div>
    );
  };

  const renderPaymentStep = () => {
    if (loading) {
      return (
        <div className="deposit-loading">
          <div className="deposit-loading-spinner"></div>
          <p>Creating your deposit request...</p>
        </div>
      );
    }

    if (!currentDeposit) {
      return (
        <div className="deposit-error">
          <p>Failed to create deposit request. Please try again.</p>
          <button onClick={() => setStep(2)} className="deposit-retry-btn">
            Try Again
          </button>
        </div>
      );
    }

    return (
      <div className="deposit-step">
        <div className="deposit-step-header">
          <button onClick={resetDeposit} className="deposit-back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="15,18 9,12 15,6"/>
            </svg>
            New Deposit
          </button>
          <h2>Complete Payment</h2>
          <p>Send exactly {currentDeposit.formatted_amount} {currentDeposit.cryptocurrency} to the address below</p>
        </div>

        <div className="deposit-payment-container">
          <div className="deposit-payment-main">
            <div className="deposit-payment-summary">
              <div className="deposit-payment-crypto">
                <div
                  className="deposit-payment-crypto-icon"
                  style={{ backgroundColor: currentDeposit.crypto_details.color }}
                >
                  {currentDeposit.crypto_details.icon}
                </div>
                <div className="deposit-payment-crypto-info">
                  <div className="deposit-payment-crypto-amount">
                    {currentDeposit.formatted_amount} {currentDeposit.cryptocurrency}
                  </div>
                  <div className="deposit-payment-crypto-usd">
                    ${currentDeposit.amount_usd?.toFixed(2) || '0.00'} USD
                  </div>
                  <div className="deposit-payment-crypto-rate">
                    1 {currentDeposit.cryptocurrency} = ${currentDeposit.conversion_rate?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </div>
            </div>

            <div className="deposit-payment-address">
              <h3>Deposit Address</h3>
              <div className="deposit-address-container">
                <div className="deposit-address-input-wrapper">
                  <input
                    type="text"
                    value={currentDeposit.deposit_address}
                    readOnly
                    className="deposit-address-input"
                  />
                  <button
                    onClick={() => copyToClipboard(currentDeposit.deposit_address)}
                    className="deposit-copy-btn"
                    title="Copy address"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                      <path d="M5,15H4a2,2,0,0,1-2-2V4A2,2,0,0,1,4,2H15a2,2,0,0,1,2,2V5"/>
                    </svg>
                  </button>
                </div>
                <div className="deposit-network-info">
                  Network: {currentDeposit.crypto_details.network}
                </div>
              </div>
            </div>

            <div className="deposit-payment-warnings">
              <div className="deposit-warning">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M10.29,3.86L1.82,18a2,2,0,0,0,1.71,3H20.47a2,2,0,0,0,1.71-3L13.71,3.86a2,2,0,0,0-3.42,0Z"/>
                  <line x1="12" y1="9" x2="12" y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                <div>
                  <strong>Important:</strong> Send only {currentDeposit.cryptocurrency} to this address.
                  Sending other cryptocurrencies will result in permanent loss.
                </div>
              </div>
              <div className="deposit-warning">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <polyline points="12,6 12,12 16,14"/>
                </svg>
                <div>
                  <strong>Confirmation Time:</strong> Your deposit will be credited after {currentDeposit.required_confirmations} network confirmations.
                </div>
              </div>
            </div>

            <div className="deposit-payment-status">
              <div className="deposit-status-item">
                <span className="deposit-status-label">Status:</span>
                <span className={`deposit-status-value ${currentDeposit.status}`}>
                  {depositService.getStatusDisplay(currentDeposit.status)}
                </span>
              </div>
              <div className="deposit-status-item">
                <span className="deposit-status-label">Confirmations:</span>
                <span className="deposit-status-value">
                  {currentDeposit.confirmations || 0} / {currentDeposit.required_confirmations}
                </span>
              </div>
              <div className="deposit-status-item">
                <span className="deposit-status-label">Expires:</span>
                <span className="deposit-status-value">
                  {depositService.getTimeRemaining(currentDeposit.expires_at)}
                </span>
              </div>
              {currentDeposit.transaction_hash && (
                <div className="deposit-status-item">
                  <span className="deposit-status-label">Transaction:</span>
                  <span className="deposit-status-value transaction-hash">
                    {currentDeposit.transaction_hash.substring(0, 10)}...
                    <button
                      onClick={() => copyToClipboard(currentDeposit.transaction_hash)}
                      className="deposit-copy-hash-btn"
                      title="Copy transaction hash"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        <path d="M5,15H4a2,2,0,0,1-2-2V4A2,2,0,0,1,4,2H15a2,2,0,0,1,2,2V5"/>
                      </svg>
                    </button>
                  </span>
                </div>
              )}
            </div>

            {/* Status Update Notifications */}
            {depositStatusUpdates[currentDeposit.id] && (
              <div className="deposit-status-notification">
                <div className="deposit-status-notification-content">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                    <polyline points="22,4 12,14.01 9,11.01"/>
                  </svg>
                  <div>
                    <strong>Status Updated!</strong>
                    <p>Your deposit status changed to: {depositService.getStatusDisplay(depositStatusUpdates[currentDeposit.id].newStatus)}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="deposit-payment-sidebar">
            <div className="deposit-qr-section">
              <h3>QR Code</h3>
              <div className="deposit-qr-container">
                {currentDeposit.qr_code_data ? (
                  <img
                    src={currentDeposit.qr_code_data}
                    alt="Deposit QR Code"
                    className="deposit-qr-code"
                  />
                ) : (
                  <div className="deposit-qr-placeholder">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                      <rect x="7" y="7" width="3" height="3"/>
                      <rect x="14" y="7" width="3" height="3"/>
                      <rect x="7" y="14" width="3" height="3"/>
                    </svg>
                    <p>QR Code</p>
                  </div>
                )}
              </div>
              <p className="deposit-qr-instruction">
                Scan with your crypto wallet to pay
              </p>
            </div>

            <div className="deposit-instructions">
              <h3>Instructions</h3>
              <ol>
                <li>Copy the deposit address above</li>
                <li>Open your {currentDeposit.cryptocurrency} wallet</li>
                <li>Send exactly {currentDeposit.formatted_amount} {currentDeposit.cryptocurrency}</li>
                <li>Wait for network confirmations</li>
                <li>Your balance will be updated automatically</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderDepositHistory = () => {
    if (depositHistory.length === 0) return null;

    return (
      <div className="deposit-history-section">
        <h3>Recent Deposits</h3>
        <div className="deposit-history-list">
          {depositHistory.map(deposit => {
            const formatted = depositService.formatDepositForDisplay(deposit);
            return (
              <div key={deposit.id} className="deposit-history-item">
                <div className="deposit-history-crypto">
                  <div
                    className="deposit-history-crypto-icon"
                    style={{ backgroundColor: formatted.crypto_details?.color || '#666' }}
                  >
                    {formatted.crypto_details?.icon || '?'}
                  </div>
                  <div className="deposit-history-crypto-info">
                    <div className="deposit-history-amount">
                      {formatted.formatted_crypto_amount} {deposit.cryptocurrency}
                    </div>
                    <div className="deposit-history-usd">
                      {formatted.formatted_usd_amount}
                    </div>
                  </div>
                </div>
                <div className="deposit-history-status">
                  <span className={`deposit-history-status-badge ${deposit.status}`}>
                    {formatted.status_display}
                  </span>
                  <div className="deposit-history-date">
                    {new Date(deposit.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="deposit">
      <div className="deposit-header">
        <h1>Deposit Funds</h1>
        <p>Add funds to your BullSeed account using cryptocurrency</p>
      </div>

      <div className="deposit-content">
        <div className="deposit-main">
          {step === 1 && renderAmountStep()}
          {step === 2 && renderCryptoStep()}
          {step === 3 && renderPaymentStep()}
        </div>

        <div className="deposit-sidebar">
          {renderDepositHistory()}

          <div className="deposit-support">
            <h3>Need Help?</h3>
            <p>If you're having trouble with your deposit, our support team is here to help.</p>
            <button className="deposit-support-btn">
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Deposit;
