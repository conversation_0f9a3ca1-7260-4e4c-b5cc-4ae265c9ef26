/* KYC Styles */
.kyc-status,
.kyc-application {
  padding: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.kyc-status-header,
.kyc-application-header {
  margin-bottom: 32px;
  text-align: center;
}

.kyc-status-header h1,
.kyc-application-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.kyc-status-header p,
.kyc-application-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.kyc-status-content,
.kyc-application-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* KYC Status Styles */
.kyc-overall-status {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.kyc-overall-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.kyc-overall-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.kyc-overall-info h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.kyc-overall-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.kyc-progress {
  margin-top: 20px;
}

.kyc-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.kyc-progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.kyc-progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.kyc-steps {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.kyc-steps h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.kyc-steps-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kyc-step {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.kyc-step.completed {
  border-color: rgba(0, 212, 170, 0.3);
  background: rgba(0, 212, 170, 0.05);
}

.kyc-step.pending {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.kyc-step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.kyc-step.completed .kyc-step-number {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.kyc-step.pending .kyc-step-number {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.kyc-step-content {
  flex: 1;
}

.kyc-step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.kyc-step-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.kyc-step-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.kyc-step-status-icon {
  width: 16px;
  height: 16px;
}

.kyc-step-status-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kyc-step-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.kyc-step-completed {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.kyc-actions {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.kyc-action-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;
  display: inline-block;
}

.kyc-action-btn.primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: none;
}

.kyc-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.kyc-action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyc-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.kyc-help {
  text-align: center;
}

.kyc-help h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.kyc-help p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 16px;
}

.kyc-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.kyc-info-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
}

.kyc-info-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.kyc-info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kyc-info-card li {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.kyc-info-card li::before {
  content: '•';
  color: #00d4aa;
  position: absolute;
  left: 0;
}

/* KYC Application Styles */
.kyc-steps-progress {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.kyc-step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.kyc-step-item.active,
.kyc-step-item.completed {
  opacity: 1;
}

.kyc-step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 8px;
}

.kyc-step-item.active .kyc-step-icon {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.kyc-step-item.completed .kyc-step-icon {
  background: rgba(0, 212, 170, 0.3);
  color: #00d4aa;
}

.kyc-step-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 600;
}

.kyc-form {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.kyc-form-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.kyc-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.kyc-form-group {
  display: flex;
  flex-direction: column;
}

.kyc-form-group.full-width {
  grid-column: 1 / -1;
}

.kyc-form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.kyc-upload-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.kyc-upload-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.kyc-upload-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kyc-upload-area {
  position: relative;
}

.kyc-upload-area input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.kyc-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.kyc-upload-label:hover {
  border-color: rgba(0, 212, 170, 0.5);
  background: rgba(0, 212, 170, 0.05);
}

.kyc-upload-label span {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.kyc-upload-label small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.kyc-upload-preview {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: #00d4aa;
}

.kyc-review {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.kyc-review-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.kyc-review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
}

.kyc-review-item:last-child {
  border-bottom: none;
}

.kyc-review-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.kyc-review-item span:last-child {
  color: white;
  font-weight: 600;
}

.kyc-terms {
  margin-top: 20px;
}

.kyc-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
}

.kyc-checkbox input[type="checkbox"] {
  margin: 0;
  width: auto;
}

.kyc-checkbox span {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.kyc-form-actions {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-top: 24px;
}

.kyc-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
}

.kyc-btn.primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
}

.kyc-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.kyc-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyc-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .kyc-status,
  .kyc-application {
    padding: 16px;
  }
  
  .kyc-status-header h1,
  .kyc-application-header h1 {
    font-size: 24px;
  }
  
  .kyc-info {
    grid-template-columns: 1fr;
  }
  
  .kyc-steps-progress {
    flex-direction: column;
    gap: 16px;
  }
  
  .kyc-step-item {
    flex-direction: row;
    text-align: left;
  }
  
  .kyc-form-grid {
    grid-template-columns: 1fr;
  }
  
  .kyc-upload-section {
    grid-template-columns: 1fr;
  }
  
  .kyc-form-actions {
    flex-direction: column;
  }
}

/* Error Message */
.kyc-error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  color: #ef4444;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Loading and Error States for KYC Status */
.kyc-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.kyc-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.kyc-error {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin: 20px 0;
}

.kyc-error p {
  color: #ef4444;
  margin-bottom: 20px;
}

.kyc-retry-btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kyc-retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.kyc-not-started {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin: 20px 0;
}

.kyc-not-started p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

/* Disabled button states */
.kyc-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}
