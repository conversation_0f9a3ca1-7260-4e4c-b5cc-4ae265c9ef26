import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import referralService from '../../services/referralService';
import '../styles/Referrals.css';

const Referrals = ({ user }) => {
  const [referralStats, setReferralStats] = useState({
    totalJoined: 0,
    referralEarn: 0,
    referrals: []
  });
  const [referralEarnings, setReferralEarnings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const referralLink = user?.referral_code 
    ? `${window.location.origin}/registration?ref=${user.referral_code}`
    : '';

  useEffect(() => {
    if (user?.auth_id) {
      loadReferralData();
    }
  }, [user]);

  const loadReferralData = async () => {
    try {
      setLoading(true);
      
      // Get referral statistics
      const stats = await referralService.getReferralStats(user.auth_id);
      setReferralStats(stats);

      // Get detailed earnings history
      const { data: earnings, error } = await supabase
        .from('referral_earnings')
        .select(`
          *,
          referred_user:users!referral_earnings_referred_id_fkey(name, email)
        `)
        .eq('referrer_id', user.auth_id)
        .order('created_at', { ascending: false });

      if (!error && earnings) {
        setReferralEarnings(earnings);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      completed: 'status-completed',
      pending: 'status-pending',
      cancelled: 'status-cancelled'
    };
    
    return (
      <span className={`status-badge ${statusClasses[status] || ''}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getLevelBadge = (level) => {
    const levelColors = {
      1: '#00d4aa',
      2: '#3b82f6',
      3: '#8b5cf6'
    };
    
    return (
      <span 
        className="level-badge" 
        style={{ backgroundColor: levelColors[level] || '#6b7280' }}
      >
        Level {level}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="referrals-page">
        <div className="referrals-loading">
          <div className="loading-spinner"></div>
          <span>Loading referral data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="referrals-page">
      <div className="referrals-header">
        <h1>Referral Program</h1>
        <p>Earn rewards by inviting friends to BullSeed</p>
      </div>

      {/* Referral Link Section */}
      <div className="referral-link-section">
        <div className="referral-link-card">
          <div className="referral-link-header">
            <h3>Your Referral Link</h3>
            <div className="referral-commission-info">
              <span className="commission-rate">2.5%</span>
              <span className="commission-label">Commission</span>
            </div>
          </div>
          
          <div className="referral-link-container">
            <div className="referral-link-input">
              <input 
                type="text" 
                value={referralLink} 
                readOnly 
                className="referral-link-field"
              />
              <button 
                onClick={handleCopyLink}
                className={`copy-button ${copied ? 'copied' : ''}`}
              >
                {copied ? (
                  <>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                    </svg>
                    Copy
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="share-buttons">
            <button 
              onClick={() => window.open(`https://twitter.com/intent/tweet?text=Join me on BullSeed and start earning crypto rewards!&url=${encodeURIComponent(referralLink)}`, '_blank')}
              className="share-button twitter"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
              Share on Twitter
            </button>
            
            <button 
              onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`, '_blank')}
              className="share-button facebook"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              Share on Facebook
            </button>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="referral-stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-value">{referralStats.totalJoined}</div>
            <div className="stat-label">Total Referrals</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="1" x2="12" y2="23"/>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-value">${referralStats.referralEarn.toFixed(2)}</div>
            <div className="stat-label">Total Earned</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="1" y="3" width="15" height="13"/>
              <polygon points="16,8 20,8 23,11 23,16 16,16 16,8"/>
              <circle cx="5.5" cy="18.5" r="2.5"/>
              <circle cx="18.5" cy="18.5" r="2.5"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-value">${user?.referral_funds?.toFixed(2) || '0.00'}</div>
            <div className="stat-label">Available Balance</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="referral-tabs">
        <button 
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={`tab-button ${activeTab === 'earnings' ? 'active' : ''}`}
          onClick={() => setActiveTab('earnings')}
        >
          Earnings History
        </button>
        <button 
          className={`tab-button ${activeTab === 'program' ? 'active' : ''}`}
          onClick={() => setActiveTab('program')}
        >
          Program Details
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <div className="overview-content">
            <div className="referral-program-info">
              <h3>How It Works</h3>
              <div className="steps">
                <div className="step">
                  <div className="step-number">1</div>
                  <div className="step-content">
                    <h4>Share Your Link</h4>
                    <p>Share your unique referral link with friends and family</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">2</div>
                  <div className="step-content">
                    <h4>They Sign Up</h4>
                    <p>When someone signs up using your link, they become your referral</p>
                  </div>
                </div>
                <div className="step">
                  <div className="step-number">3</div>
                  <div className="step-content">
                    <h4>Earn Commissions</h4>
                    <p>Earn 2.5% commission on their deposits and investments</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'earnings' && (
          <div className="earnings-content">
            <div className="earnings-table">
              <div className="table-header">
                <div className="table-col">Date</div>
                <div className="table-col">User</div>
                <div className="table-col">Type</div>
                <div className="table-col">Investment</div>
                <div className="table-col">Commission</div>
                <div className="table-col">Level</div>
                <div className="table-col">Status</div>
              </div>
              <div className="table-body">
                {referralEarnings.length === 0 ? (
                  <div className="table-empty">
                    <p>No earnings yet. Start referring friends to earn commissions!</p>
                  </div>
                ) : (
                  referralEarnings.map((earning) => (
                    <div key={earning.id} className="table-row">
                      <div className="table-col">{formatDate(earning.created_at)}</div>
                      <div className="table-col">
                        {earning.referred_user?.name || 'Anonymous User'}
                      </div>
                      <div className="table-col">
                        <span className="investment-type">{earning.investment_type}</span>
                      </div>
                      <div className="table-col">${earning.investment_amount}</div>
                      <div className="table-col commission-amount">
                        ${earning.commission_amount}
                      </div>
                      <div className="table-col">
                        {getLevelBadge(earning.referral_level)}
                      </div>
                      <div className="table-col">
                        {getStatusBadge(earning.status)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'program' && (
          <div className="program-content">
            <div className="program-details">
              <h3>Multi-Level Referral Program</h3>
              <div className="level-cards">
                <div className="level-card">
                  <div className="level-header">
                    <span className="level-badge" style={{backgroundColor: '#00d4aa'}}>Level 1</span>
                    <span className="level-rate">1%</span>
                  </div>
                  <h4>Direct Referrals</h4>
                  <p>Earn 1% commission on investments made by users you directly refer</p>
                </div>
                
                <div className="level-card">
                  <div className="level-header">
                    <span className="level-badge" style={{backgroundColor: '#3b82f6'}}>Level 2</span>
                    <span className="level-rate">2%</span>
                  </div>
                  <h4>Second-Tier Referrals</h4>
                  <p>Earn 2% commission on investments made by users referred by your referrals</p>
                </div>
                
                <div className="level-card">
                  <div className="level-header">
                    <span className="level-badge" style={{backgroundColor: '#8b5cf6'}}>Level 3</span>
                    <span className="level-rate">6%</span>
                  </div>
                  <h4>Third-Tier Referrals</h4>
                  <p>Earn 6% commission on investments made by third-level referrals</p>
                </div>
              </div>
              
              <div className="program-terms">
                <h4>Terms & Conditions</h4>
                <ul>
                  <li>Commissions are calculated on successful deposits and investments</li>
                  <li>Earnings are credited to your referral balance immediately</li>
                  <li>Minimum withdrawal amount is $50</li>
                  <li>Referral relationships are permanent once established</li>
                  <li>BullSeed reserves the right to modify the program terms</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Referrals;
