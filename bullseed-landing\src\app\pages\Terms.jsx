import React from 'react';
import { Link } from 'react-router-dom';
import '../styles/Auth.css';

const Terms = () => {
  return (
    <div className="auth-container">
      <div className="auth-left">
        <div className="auth-brand">
          <div className="brand-logo">
            <div className="logo-icon">₿</div>
            <span className="brand-name">BullSeed</span>
          </div>
        </div>
        
        <div className="auth-welcome">
          <h1>Terms of Service</h1>
          <p>Please read our terms and conditions carefully before using our platform.</p>
          
          <div className="auth-switch">
            <Link to="/signup" className="auth-link">← Back to Sign Up</Link>
          </div>
        </div>
      </div>

      <div className="auth-right">
        <div className="auth-form-container">
          <div className="terms-content">
            <h2>BullSeed Terms of Service</h2>
            
            <div className="terms-section">
              <h3>1. Acceptance of Terms</h3>
              <p>By accessing and using BullSeed's services, you accept and agree to be bound by the terms and provision of this agreement.</p>
            </div>

            <div className="terms-section">
              <h3>2. Investment Risks</h3>
              <p>Cryptocurrency investments carry inherent risks. Past performance does not guarantee future results. You should only invest what you can afford to lose.</p>
            </div>

            <div className="terms-section">
              <h3>3. Account Security</h3>
              <p>You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>
            </div>

            <div className="terms-section">
              <h3>4. Service Availability</h3>
              <p>We strive to maintain service availability but cannot guarantee uninterrupted access to our platform.</p>
            </div>

            <div className="terms-section">
              <h3>5. Limitation of Liability</h3>
              <p>BullSeed shall not be liable for any indirect, incidental, special, consequential, or punitive damages.</p>
            </div>

            <div className="terms-section">
              <h3>6. Changes to Terms</h3>
              <p>We reserve the right to modify these terms at any time. Continued use of our services constitutes acceptance of modified terms.</p>
            </div>

            <div className="terms-actions">
              <Link to="/signup" className="auth-submit-btn">I Agree - Continue to Sign Up</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terms;
