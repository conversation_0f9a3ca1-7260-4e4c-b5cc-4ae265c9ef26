// Formatting service for currency, dates, and numbers based on user preferences
class FormattingService {
  constructor() {
    this.currencySymbols = {
      USD: '$',
      EUR: '€',
      GBP: '£',
      JPY: '¥',
      CAD: 'C$',
      AUD: 'A$',
      CHF: 'CHF',
      CNY: '¥',
      KRW: '₩',
      INR: '₹'
    };

    this.currencyLocales = {
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      JPY: 'ja-JP',
      CAD: 'en-CA',
      AUD: 'en-AU',
      CHF: 'de-CH',
      CNY: 'zh-CN',
      KRW: 'ko-KR',
      INR: 'en-IN'
    };
  }

  // Format currency based on user's currency preference
  formatCurrency(amount, currency = 'USD', options = {}) {
    const {
      minimumFractionDigits = 2,
      maximumFractionDigits = 2,
      showSymbol = true
    } = options;

    if (amount === null || amount === undefined || isNaN(amount)) {
      return showSymbol ? `${this.currencySymbols[currency] || '$'}0.00` : '0.00';
    }

    const locale = this.currencyLocales[currency] || 'en-US';
    
    try {
      const formatted = new Intl.NumberFormat(locale, {
        style: showSymbol ? 'currency' : 'decimal',
        currency: currency,
        minimumFractionDigits,
        maximumFractionDigits
      }).format(amount);

      return formatted;
    } catch (error) {
      // Fallback formatting
      const symbol = showSymbol ? (this.currencySymbols[currency] || '$') : '';
      return `${symbol}${amount.toLocaleString('en-US', { 
        minimumFractionDigits, 
        maximumFractionDigits 
      })}`;
    }
  }

  // Format crypto prices with appropriate decimal places
  formatCryptoPrice(price, currency = 'USD') {
    if (price === null || price === undefined || isNaN(price)) {
      return this.formatCurrency(0, currency);
    }

    let decimals;
    if (price >= 1000) {
      decimals = 2;
    } else if (price >= 1) {
      decimals = 4;
    } else if (price >= 0.01) {
      decimals = 6;
    } else {
      decimals = 8;
    }

    return this.formatCurrency(price, currency, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  // Format large numbers (market cap, volume)
  formatLargeNumber(number, currency = 'USD') {
    if (number === null || number === undefined || isNaN(number)) {
      return this.formatCurrency(0, currency);
    }

    const symbol = this.currencySymbols[currency] || '$';

    if (number >= 1e12) {
      return `${symbol}${(number / 1e12).toFixed(2)}T`;
    } else if (number >= 1e9) {
      return `${symbol}${(number / 1e9).toFixed(2)}B`;
    } else if (number >= 1e6) {
      return `${symbol}${(number / 1e6).toFixed(2)}M`;
    } else if (number >= 1e3) {
      return `${symbol}${(number / 1e3).toFixed(2)}K`;
    } else {
      return this.formatCurrency(number, currency);
    }
  }

  // Format percentage
  formatPercentage(value, decimals = 2) {
    if (value === null || value === undefined || isNaN(value)) {
      return '0.00%';
    }

    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(decimals)}%`;
  }

  // Format date based on timezone
  formatDate(date, timezone = 'America/New_York', options = {}) {
    if (!date) return '';

    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: timezone
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
      return new Intl.DateTimeFormat('en-US', formatOptions).format(new Date(date));
    } catch (error) {
      // Fallback to simple date formatting
      return new Date(date).toLocaleString();
    }
  }

  // Format date for display (shorter format)
  formatDisplayDate(date, timezone = 'America/New_York') {
    return this.formatDate(date, timezone, {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Format time only
  formatTime(date, timezone = 'America/New_York') {
    return this.formatDate(date, timezone, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  // Format crypto amount with appropriate decimals
  formatCryptoAmount(amount, cryptoSymbol) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '0.00000000';
    }

    let decimals;
    switch (cryptoSymbol?.toUpperCase()) {
      case 'BTC':
        decimals = 8;
        break;
      case 'ETH':
      case 'BNB':
        decimals = 6;
        break;
      case 'USDT':
      case 'USDC':
        decimals = 2;
        break;
      default:
        decimals = 8;
    }

    return amount.toFixed(decimals);
  }

  // Get timezone display name
  getTimezoneDisplayName(timezone) {
    const timezoneNames = {
      'America/New_York': 'Eastern Time (ET)',
      'America/Chicago': 'Central Time (CT)',
      'America/Denver': 'Mountain Time (MT)',
      'America/Los_Angeles': 'Pacific Time (PT)',
      'Europe/London': 'Greenwich Mean Time (GMT)',
      'Europe/Paris': 'Central European Time (CET)',
      'Europe/Berlin': 'Central European Time (CET)',
      'Asia/Tokyo': 'Japan Standard Time (JST)',
      'Asia/Shanghai': 'China Standard Time (CST)',
      'Asia/Seoul': 'Korea Standard Time (KST)',
      'Asia/Kolkata': 'India Standard Time (IST)',
      'Australia/Sydney': 'Australian Eastern Time (AET)',
      'UTC': 'Coordinated Universal Time (UTC)'
    };

    return timezoneNames[timezone] || timezone;
  }

  // Get currency display name
  getCurrencyDisplayName(currency) {
    const currencyNames = {
      USD: 'US Dollar ($)',
      EUR: 'Euro (€)',
      GBP: 'British Pound (£)',
      JPY: 'Japanese Yen (¥)',
      CAD: 'Canadian Dollar (C$)',
      AUD: 'Australian Dollar (A$)',
      CHF: 'Swiss Franc (CHF)',
      CNY: 'Chinese Yuan (¥)',
      KRW: 'South Korean Won (₩)',
      INR: 'Indian Rupee (₹)'
    };

    return currencyNames[currency] || currency;
  }

  // Get supported currencies
  getSupportedCurrencies() {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
      { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
      { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
      { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
      { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
      { code: 'KRW', name: 'South Korean Won', symbol: '₩' },
      { code: 'INR', name: 'Indian Rupee', symbol: '₹' }
    ];
  }

  // Get supported timezones
  getSupportedTimezones() {
    return [
      { code: 'America/New_York', name: 'Eastern Time (ET)' },
      { code: 'America/Chicago', name: 'Central Time (CT)' },
      { code: 'America/Denver', name: 'Mountain Time (MT)' },
      { code: 'America/Los_Angeles', name: 'Pacific Time (PT)' },
      { code: 'Europe/London', name: 'Greenwich Mean Time (GMT)' },
      { code: 'Europe/Paris', name: 'Central European Time (CET)' },
      { code: 'Europe/Berlin', name: 'Central European Time (CET)' },
      { code: 'Asia/Tokyo', name: 'Japan Standard Time (JST)' },
      { code: 'Asia/Shanghai', name: 'China Standard Time (CST)' },
      { code: 'Asia/Seoul', name: 'Korea Standard Time (KST)' },
      { code: 'Asia/Kolkata', name: 'India Standard Time (IST)' },
      { code: 'Australia/Sydney', name: 'Australian Eastern Time (AET)' },
      { code: 'UTC', name: 'Coordinated Universal Time (UTC)' }
    ];
  }
}

export const formattingService = new FormattingService();
