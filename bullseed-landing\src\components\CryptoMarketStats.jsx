import React, { useState, useEffect } from 'react';
import cryptoService from '../services/cryptoService';

const CryptoMarketStats = () => {
  const [cryptoData, setCryptoData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Use centralized crypto service for consistent real-time updates
  useEffect(() => {
    const fetchCryptoData = async () => {
      try {
        // Get more coins for the ticker display
        const data = await cryptoService.getAllCoins(20);

        // Format the data for our component
        const formattedData = data.map(coin => ({
          id: coin.id,
          symbol: coin.symbol.toUpperCase(),
          name: coin.name,
          price: coin.current_price,
          change: coin.price_change_percentage_24h,
          logo: coin.image,
          marketCap: coin.market_cap
        }));

        // Duplicate data for seamless scrolling
        setCryptoData([...formattedData, ...formattedData]);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching crypto data:', error);
        setLoading(false);
      }
    };

    fetchCryptoData();

    // Set up real-time updates using centralized service with faster intervals
    const cleanup = cryptoService.startTickerUpdates(async () => {
      try {
        const data = await cryptoService.getAllCoins(20);
        const formattedData = data.map(coin => ({
          id: coin.id,
          symbol: coin.symbol.toUpperCase(),
          name: coin.name,
          price: coin.current_price,
          change: coin.price_change_percentage_24h,
          logo: coin.image,
          marketCap: coin.market_cap
        }));
        setCryptoData([...formattedData, ...formattedData]);

        // Add visual indicator that data updated
        const container = document.querySelector('.crypto-market-stats');
        if (container) {
          container.classList.add('data-updated');
          setTimeout(() => container.classList.remove('data-updated'), 300);
        }
      } catch (error) {
        console.error('Error in ticker update:', error);
      }
    }, 3000); // Update every 3 seconds for ticker

    return cleanup;
  }, []);

  const formatPrice = (price) => {
    if (price < 1) {
      return `$${price.toFixed(6)}`;
    } else if (price < 100) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };

  const formatChange = (change) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <section className="crypto-market-stats">
        <div className="crypto-market-container">
          <div className="crypto-loading">Loading market data...</div>
        </div>
      </section>
    );
  }

  return (
    <section className="crypto-market-stats">
      <div className="crypto-market-container">
        <div className="crypto-market-track">
          {cryptoData.map((coin, index) => (
            <div key={`${coin.id}-${index}`} className="crypto-stat-card">
              <div className="crypto-stat-icon">
                <img 
                  src={coin.logo} 
                  alt={coin.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
              <div className="crypto-stat-info">
                <div className="crypto-stat-symbol">{coin.symbol}</div>
                <div className="crypto-stat-price">{formatPrice(coin.price)}</div>
                <div className={`crypto-stat-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {formatChange(coin.change)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CryptoMarketStats;
