import { supabase } from '../lib/supabase';

class ReferralService {
  // Generate a unique referral code for a user
  generateReferralCode() {
    return 'BS' + Math.random().toString(36).substring(2, 10).toUpperCase();
  }

  // Get user by referral code
  async getUserByReferralCode(referralCode) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, auth_id, referral_code, name, email')
        .eq('referral_code', referralCode)
        .single();

      if (error) {
        console.error('Error finding user by referral code:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getUserByReferralCode:', error);
      return null;
    }
  }

  // Create referral relationship when a user signs up with a referral code
  async createReferral(referrerAuthId, referredAuthId, referralCode) {
    try {
      // First, update the referred user to mark who referred them
      const { error: updateError } = await supabase
        .from('users')
        .update({ referred_by: referrerAuthId })
        .eq('auth_id', referredAuthId);

      if (updateError) {
        console.error('Error updating referred user:', updateError);
        return false;
      }

      // Create referral record
      const { data, error } = await supabase
        .from('referrals')
        .insert({
          referrer_id: referrerAuthId,
          referred_id: referredAuthId,
          referral_code: referralCode,
          status: 'active',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating referral record:', error);
        return false;
      }

      console.log('Referral created successfully:', data);
      return true;
    } catch (error) {
      console.error('Error in createReferral:', error);
      return false;
    }
  }

  // Calculate and award referral commission
  async awardReferralCommission(referredUserId, investmentAmount, investmentType = 'deposit') {
    try {
      // Get the referred user's referrer
      const { data: referredUser, error: userError } = await supabase
        .from('users')
        .select('referred_by')
        .eq('auth_id', referredUserId)
        .single();

      if (userError || !referredUser?.referred_by) {
        console.log('No referrer found for user:', referredUserId);
        return false;
      }

      const referrerId = referredUser.referred_by;

      // Calculate commission based on your referral program
      // Using 2.5% as shown in your marketing materials
      const commissionRate = 0.025; // 2.5%
      const commissionAmount = parseFloat(investmentAmount) * commissionRate;

      // Create referral earning record
      const { data: earning, error: earningError } = await supabase
        .from('referral_earnings')
        .insert({
          referrer_id: referrerId,
          referred_id: referredUserId,
          investment_amount: parseFloat(investmentAmount),
          commission_amount: commissionAmount,
          commission_rate: commissionRate,
          investment_type: investmentType,
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (earningError) {
        console.error('Error creating referral earning:', earningError);
        return false;
      }

      // Update referrer's referral funds
      const { error: updateError } = await supabase
        .from('users')
        .update({
          referral_funds: supabase.sql`referral_funds + ${commissionAmount}`
        })
        .eq('auth_id', referrerId);

      if (updateError) {
        console.error('Error updating referrer funds:', updateError);
        return false;
      }

      // Mark earning as completed
      await supabase
        .from('referral_earnings')
        .update({ status: 'completed' })
        .eq('id', earning.id);

      console.log(`Referral commission awarded: $${commissionAmount} to user ${referrerId}`);
      return true;
    } catch (error) {
      console.error('Error in awardReferralCommission:', error);
      return false;
    }
  }

  // Get referral statistics for a user
  async getReferralStats(userId) {
    try {
      // Get total referrals
      const { data: referrals, error: referralsError } = await supabase
        .from('referrals')
        .select('id, created_at, referred_id')
        .eq('referrer_id', userId);

      if (referralsError) {
        console.error('Error fetching referrals:', referralsError);
        return {
          totalJoined: 0,
          referralEarn: 0,
          referrals: []
        };
      }

      // Get total earnings
      const { data: earnings, error: earningsError } = await supabase
        .from('referral_earnings')
        .select('commission_amount')
        .eq('referrer_id', userId)
        .eq('status', 'completed');

      if (earningsError) {
        console.error('Error fetching earnings:', earningsError);
        return {
          totalJoined: referrals?.length || 0,
          referralEarn: 0,
          referrals: referrals || []
        };
      }

      const totalEarnings = earnings?.reduce((sum, earning) => 
        sum + parseFloat(earning.commission_amount), 0) || 0;

      return {
        totalJoined: referrals?.length || 0,
        referralEarn: totalEarnings,
        referrals: referrals || []
      };
    } catch (error) {
      console.error('Error in getReferralStats:', error);
      return {
        totalJoined: 0,
        referralEarn: 0,
        referrals: []
      };
    }
  }

  // Get referral link for a user
  getReferralLink(referralCode) {
    const baseUrl = window.location.origin;
    return `${baseUrl}/registration?ref=${referralCode}`;
  }

  // Multi-level referral calculation (for your 3-tier system)
  async awardMultiLevelCommission(referredUserId, investmentAmount) {
    try {
      const levels = [
        { level: 1, rate: 0.01 }, // 1%
        { level: 2, rate: 0.02 }, // 2%
        { level: 3, rate: 0.06 }  // 6%
      ];

      let currentUserId = referredUserId;

      for (const { level, rate } of levels) {
        // Get the referrer of the current user
        const { data: user, error } = await supabase
          .from('users')
          .select('referred_by')
          .eq('auth_id', currentUserId)
          .single();

        if (error || !user?.referred_by) {
          console.log(`No level ${level} referrer found`);
          break;
        }

        const referrerId = user.referred_by;
        const commissionAmount = parseFloat(investmentAmount) * rate;

        // Create multi-level earning record
        await supabase
          .from('referral_earnings')
          .insert({
            referrer_id: referrerId,
            referred_id: referredUserId,
            investment_amount: parseFloat(investmentAmount),
            commission_amount: commissionAmount,
            commission_rate: rate,
            referral_level: level,
            investment_type: 'multi_level',
            status: 'completed',
            created_at: new Date().toISOString()
          });

        // Update referrer's funds
        await supabase
          .from('users')
          .update({
            referral_funds: supabase.sql`referral_funds + ${commissionAmount}`
          })
          .eq('auth_id', referrerId);

        console.log(`Level ${level} commission: $${commissionAmount} to user ${referrerId}`);

        // Move up the chain
        currentUserId = referrerId;
      }

      return true;
    } catch (error) {
      console.error('Error in awardMultiLevelCommission:', error);
      return false;
    }
  }
}

export default new ReferralService();
