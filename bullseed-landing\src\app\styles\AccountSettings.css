/* Account Settings Styles */
.account-settings {
  padding: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Loading State */
.account-settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.account-settings-loading p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

.account-settings-header {
  margin-bottom: 32px;
}

.account-settings-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.account-settings-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.account-settings-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.settings-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
}

.settings-tab.active {
  color: #00d4aa;
  border-bottom-color: #00d4aa;
  background: rgba(0, 212, 170, 0.05);
}

.settings-content {
  padding: 24px;
}

.settings-tab-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Error and Success Messages */
.settings-error,
.settings-success {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 24px;
}

.settings-error {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

.settings-success {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
}

.settings-error svg,
.settings-success svg {
  flex-shrink: 0;
}

.settings-section {
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.settings-section p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
  line-height: 1.5;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

/* Password Validation Errors */
.password-errors {
  margin-top: 8px;
}

.password-error {
  color: #ff6b6b;
  font-size: 12px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.password-error:before {
  content: "•";
  color: #ff6b6b;
}

.settings-form-group {
  display: flex;
  flex-direction: column;
}

.settings-form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.settings-form-group input,
.settings-form-group select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.settings-form-group input:focus,
.settings-form-group select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.settings-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  border: none;
}

.settings-btn.primary {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
}

.settings-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.settings-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.settings-btn.danger {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.settings-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.settings-2fa {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.settings-2fa-status {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.settings-2fa-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.settings-2fa-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.settings-2fa-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.settings-sessions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-session {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.settings-session-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.settings-session-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.settings-session-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.settings-session-current {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.settings-session-revoke {
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  color: #dc2626;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-session-revoke:hover {
  background: rgba(220, 38, 38, 0.15);
}

.settings-toggles {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-toggle {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.settings-toggle-info {
  flex: 1;
}

.settings-toggle-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.settings-toggle-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  margin: 0;
}

.settings-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  flex-shrink: 0;
}

.settings-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.settings-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.3s;
  border-radius: 24px;
}

.settings-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .settings-slider {
  background-color: #00d4aa;
}

input:checked + .settings-slider:before {
  transform: translateX(24px);
}

.settings-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 200px;
  margin-bottom: 16px;
}

.settings-actions-info {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 16px;
}

.settings-actions-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.settings-actions-info p:last-child {
  margin-bottom: 0;
}

.settings-actions-info strong {
  color: white;
}

/* Sessions Empty State */
.settings-sessions-empty {
  text-align: center;
  padding: 32px;
  color: rgba(255, 255, 255, 0.6);
}

.settings-sessions-empty p {
  font-size: 14px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .account-settings {
    padding: 16px;
  }
  
  .account-settings-header h1 {
    font-size: 24px;
  }
  
  .settings-tabs {
    flex-direction: column;
  }
  
  .settings-tab {
    padding: 12px 16px;
    border-bottom: none;
    border-right: 2px solid transparent;
  }
  
  .settings-tab.active {
    border-bottom: none;
    border-right-color: #00d4aa;
  }
  
  .settings-content {
    padding: 16px;
  }
  
  .settings-2fa {
    flex-direction: column;
    align-items: stretch;
  }
  
  .settings-session {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .settings-toggle {
    flex-direction: column;
    gap: 12px;
  }
  
  .settings-switch {
    align-self: flex-start;
  }
}
