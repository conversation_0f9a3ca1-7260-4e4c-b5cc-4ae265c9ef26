/* Invest Page Styles */
.invest {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
}

.invest-header {
  margin-bottom: 32px;
  text-align: center;
}

.invest-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.invest-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.balance-display {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 20px;
  backdrop-filter: blur(10px);
}

.balance-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.balance-amount {
  font-size: 18px;
  font-weight: 700;
  color: #10b981;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.invest-content {
  width: 100%;
}

.investment-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.investment-plan-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.investment-plan-card:hover {
  transform: translateY(-4px);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 12px 40px rgba(16, 185, 129, 0.15);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.plan-name {
  font-size: 14px;
  font-weight: 700;
  color: white;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.plan-badge {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
  background: rgba(16, 185, 129, 0.1);
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.daily-return {
  font-size: 16px;
  font-weight: 700;
  color: #10b981;
  line-height: 1;
}

.return-label {
  font-size: 10px;
  font-weight: 600;
  color: rgba(16, 185, 129, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.plan-content {
  margin-bottom: 20px;
  padding: 0;
}

.plan-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.plan-stat:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.stat-value {
  font-size: 13px;
  color: white;
  font-weight: 600;
}

/* Special styling for total return values */
.plan-stat:first-child .stat-value {
  color: #10b981;
  font-weight: 700;
  font-size: 14px;
}

.invest-btn {
  width: 100%;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 8px;
}

.invest-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.invest-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  background: rgba(255, 255, 255, 0.1);
  font-size: 11px;
}

/* No Investments Message */
.no-investments-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  margin-top: 40px;
}

.no-investments-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-investments-text h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.no-investments-text p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.start-investing-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.start-investing-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

/* Investment Modal */
.invest-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.invest-modal {
  background: rgba(26, 26, 26, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.modal-content {
  margin-bottom: 24px;
}

.plan-summary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.summary-item:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.summary-item span:last-child {
  color: white;
  font-weight: 600;
}

.amount-input-section {
  margin-bottom: 24px;
}

.amount-input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  z-index: 1;
}

.amount-input-container input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 16px 16px 16px 40px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.amount-input-container input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.amount-limits {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.balance-limit-warning {
  margin-top: 4px;
  font-size: 11px;
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 6px;
  padding: 4px 8px;
  display: inline-block;
}

.investment-preview {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.investment-preview h4 {
  font-size: 16px;
  font-weight: 600;
  color: #10b981;
  margin-bottom: 16px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.preview-item:not(:last-child) {
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
}

.preview-item.total {
  font-weight: 700;
  font-size: 16px;
  color: #10b981;
  margin-top: 8px;
  padding-top: 16px;
  border-top: 2px solid rgba(16, 185, 129, 0.3);
}

.preview-item span:first-child {
  color: rgba(255, 255, 255, 0.8);
}

.preview-item span:last-child {
  color: white;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.confirm-invest-btn {
  flex: 1;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.confirm-invest-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.confirm-invest-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .invest-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .invest-form {
    position: static;
  }
}

@media (max-width: 768px) {
  .invest {
    padding: 16px;
  }
  
  .invest-header h1 {
    font-size: 24px;
  }
  
  .investment-plans-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .investment-plan-card {
    padding: 16px;
  }

  .plan-name {
    font-size: 13px;
  }

  .daily-return {
    font-size: 14px;
  }
  
  .plan-returns {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .invest-form-card {
    padding: 20px;
  }
  
  .invest-confirmation-modal {
    padding: 24px;
    margin: 16px;
  }
}

/* Custom Alert Modal */
.alert-modal {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.alert-modal-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-modal-header.success {
  border-bottom-color: rgba(40, 167, 69, 0.3);
}

.alert-modal-header.error {
  border-bottom-color: rgba(220, 53, 69, 0.3);
}

.alert-modal-header.warning {
  border-bottom-color: rgba(255, 193, 7, 0.3);
}

.alert-icon {
  font-size: 24px;
}

.alert-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.alert-modal-body {
  padding: 20px 24px;
}

.alert-modal-body p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.alert-modal-actions {
  display: flex;
  justify-content: center;
  padding: 16px 24px 24px 24px;
}

.alert-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.alert-btn.primary {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
}

.alert-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}
