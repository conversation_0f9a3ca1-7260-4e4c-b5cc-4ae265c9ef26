/* Admin Dashboard Styles - Matching BullSeed Main App Design */
.admin-dashboard {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: white;
  padding: 24px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #0a0a0a;
  color: white;
}

.admin-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.admin-header-left h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Removed duplicate padlock - already in h1 text */

.admin-header-left p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.admin-header-right {
  display: flex;
  gap: 12px;
}

.admin-refresh-btn, .admin-logout-btn, .admin-settings-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.admin-refresh-btn {
  background:
    linear-gradient(135deg, rgba(0, 212, 170, 0.15) 0%, rgba(0, 184, 148, 0.1) 100%),
    rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  backdrop-filter: blur(10px);
}

.admin-refresh-btn:hover {
  background:
    linear-gradient(135deg, rgba(0, 212, 170, 0.25) 0%, rgba(0, 184, 148, 0.2) 100%),
    rgba(0, 0, 0, 0.3);
  border-color: #00d4aa;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.2);
}

.admin-settings-btn {
  background:
    linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 171, 0, 0.1) 100%),
    rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #ffc107;
  backdrop-filter: blur(10px);
}

.admin-settings-btn:hover {
  background:
    linear-gradient(135deg, rgba(255, 193, 7, 0.25) 0%, rgba(255, 171, 0, 0.2) 100%),
    rgba(0, 0, 0, 0.3);
  border-color: #ffc107;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
}

.admin-logout-btn {
  background:
    linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(200, 35, 51, 0.1) 100%),
    rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  backdrop-filter: blur(10px);
}

.admin-logout-btn:hover {
  background:
    linear-gradient(135deg, rgba(220, 53, 69, 0.25) 0%, rgba(200, 35, 51, 0.2) 100%),
    rgba(0, 0, 0, 0.3);
  border-color: #dc3545;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.admin-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 20px;
}

.admin-stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 700;
  flex-shrink: 0;
}

.admin-stat-icon.bitcoin {
  background: linear-gradient(135deg, #f7931a, #e6820e);
}

.admin-stat-icon.ethereum {
  background: linear-gradient(135deg, #627eea, #4f5bd5);
}

.admin-stat-icon.bnb {
  background: linear-gradient(135deg, #f3ba2f, #e0a800);
}

.admin-stat-icon.deposits {
  background: linear-gradient(135deg, #ffc107, #e0a800);
}

.admin-stat-icon.confirmed {
  background: linear-gradient(135deg, #28a745, #1e7e34);
}

.admin-stat-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: rgba(255, 255, 255, 0.8);
}

.bitcoin-address, .crypto-address {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #00d4aa;
  word-break: break-all;
  margin-bottom: 8px;
}

.bitcoin-stats, .crypto-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bitcoin-stats span, .crypto-stats span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #00d4aa;
  margin: 0 0 4px 0;
}

.admin-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
}

.section-header p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.deposits-table-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.deposits-table {
  width: 100%;
  border-collapse: collapse;
}

.deposits-table th {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.deposits-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.deposit-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info strong {
  color: white;
  font-size: 14px;
}

.user-info small {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.amount-usd {
  font-weight: 600;
  color: #00d4aa;
  font-size: 16px;
}

.amount-btc, .amount-crypto {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.amount-btc {
  color: #f7931a;
}

.amount-crypto {
  color: #00d4aa;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn.confirm {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.action-btn.confirm:hover {
  background: rgba(40, 167, 69, 0.2);
  transform: scale(1.1);
}

.action-btn.reject {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.action-btn.reject:hover {
  background: rgba(220, 53, 69, 0.2);
  transform: scale(1.1);
}

.action-btn.details {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.action-btn.details:hover {
  background: rgba(0, 212, 170, 0.2);
  transform: scale(1.1);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.no-deposits {
  padding: 40px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

/* Withdrawal Styles */
.admin-withdrawals-section {
  margin-top: 32px;
}

.withdrawals-table-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.withdrawals-table {
  width: 100%;
  border-collapse: collapse;
}

.withdrawals-table th {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.withdrawals-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.withdrawal-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.withdrawal-row.pending {
  border-left: 4px solid #ffa500;
}

.withdrawal-row.completed {
  border-left: 4px solid #28a745;
}

.withdrawal-row.failed {
  border-left: 4px solid #dc3545;
}

.address-info {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.address-short {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
}

.no-withdrawals {
  padding: 40px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.withdrawal-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  padding: 20px 0;
}

.admin-stat-icon.withdrawals {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: white;
}

.modal-body {
  padding: 24px;
}

.deposit-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.detail-item span {
  color: white;
  font-size: 14px;
}

.tx-hash {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  word-break: break-all;
  color: #00d4aa;
}

/* Custom Confirmation Modal */
.confirm-modal {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.confirm-modal-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.confirm-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.confirm-modal-body {
  padding: 20px 24px;
}

.confirm-modal-body p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.confirm-modal-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px 24px;
  justify-content: flex-end;
}

.confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.confirm-btn.cancel {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.confirm-btn.cancel:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.confirm-btn.primary {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
}

.confirm-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

.confirm-btn.danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.confirm-btn.danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Custom Alert Modal */
.alert-modal {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.alert-modal-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-modal-header.success {
  border-bottom-color: rgba(40, 167, 69, 0.3);
}

.alert-modal-header.error {
  border-bottom-color: rgba(220, 53, 69, 0.3);
}

.alert-modal-header.warning {
  border-bottom-color: rgba(255, 193, 7, 0.3);
}

.alert-icon {
  font-size: 24px;
}

.alert-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.alert-modal-body {
  padding: 20px 24px;
}

.alert-modal-body p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.alert-modal-actions {
  display: flex;
  justify-content: center;
  padding: 16px 24px 24px 24px;
}

.alert-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.alert-btn.primary {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
}

.alert-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

/* Settings Modal */
.settings-modal {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%),
    rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.settings-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.settings-modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.3), transparent);
}

.settings-modal-header h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 18px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: scale(1.05);
}

.settings-modal-body {
  padding: 32px;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  color: #ffc107;
  font-size: 14px;
  line-height: 1.5;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.form-group input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 14px 16px;
  font-size: 14px;
  color: white;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #00d4aa;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.cancel-btn, .save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.save-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .admin-header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .admin-stats {
    grid-template-columns: 1fr;
  }
  
  .admin-stat-card {
    padding: 16px;
  }
  
  .admin-stat-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
  
  .deposits-table-container {
    overflow-x: auto;
  }
  
  .deposits-table {
    min-width: 800px;
  }
  
  .deposits-table th,
  .deposits-table td {
    padding: 12px 8px;
  }
  
  .deposit-detail-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 20px;
    max-height: calc(100vh - 40px);
  }

  .install-prompt-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .install-prompt-actions {
    justify-content: center;
  }
}


