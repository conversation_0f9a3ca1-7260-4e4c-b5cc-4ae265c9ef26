import { supabase } from '../lib/supabase';

class KYCService {
  // Submit KYC application with all data and documents
  async submitKYCApplication(userId, formData) {
    try {
      console.log('Submitting KYC application for user:', userId);
      
      // First, upload all documents
      const documentUrls = {};
      const documentTypes = ['idFront', 'idBack', 'proofOfAddress', 'selfie'];
      
      for (const docType of documentTypes) {
        if (formData[docType]) {
          console.log(`Uploading ${docType}...`);
          const documentUrl = await this.uploadDocument(userId, docType, formData[docType]);
          documentUrls[docType] = documentUrl;
        }
      }
      
      // Create or update KYC application
      const applicationData = {
        user_id: userId,
        first_name: formData.firstName,
        last_name: formData.lastName,
        date_of_birth: formData.dateOfBirth,
        nationality: formData.nationality,
        phone_number: formData.phoneNumber,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        postal_code: formData.postalCode,
        country: formData.country,
        id_type: formData.idType,
        status: 'pending'
      };
      
      // Insert or update KYC application
      const { data: application, error: appError } = await supabase
        .from('kyc_applications')
        .upsert(applicationData, { onConflict: 'user_id' })
        .select()
        .single();
        
      if (appError) throw appError;
      
      // Save document records
      for (const [docType, url] of Object.entries(documentUrls)) {
        await this.saveDocumentRecord(userId, docType, url);
      }
      
      // Update user's KYC status
      await this.updateUserKYCStatus(userId, 'pending');
      
      console.log('KYC application submitted successfully:', application);
      return application;
      
    } catch (error) {
      console.error('Error submitting KYC application:', error);
      throw error;
    }
  }
  
  // Upload document to Supabase Storage
  async uploadDocument(userId, documentType, file) {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/${documentType}_${Date.now()}.${fileExt}`;
      
      const { data, error } = await supabase.storage
        .from('kyc-documents')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });
        
      if (error) throw error;
      
      // Get public URL (even though bucket is private, we need the path)
      const { data: { publicUrl } } = supabase.storage
        .from('kyc-documents')
        .getPublicUrl(fileName);
        
      return publicUrl;
    } catch (error) {
      console.error(`Error uploading ${documentType}:`, error);
      throw error;
    }
  }
  
  // Save document record in database
  async saveDocumentRecord(userId, documentType, documentUrl) {
    try {
      const { data, error } = await supabase
        .from('kyc_documents')
        .upsert({
          user_id: userId,
          document_type: documentType,
          document_url: documentUrl,
          status: 'pending'
        }, { onConflict: 'user_id,document_type' })
        .select()
        .single();
        
      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error saving document record for ${documentType}:`, error);
      throw error;
    }
  }
  
  // Update user's overall KYC status
  async updateUserKYCStatus(userId, status) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({ 
          kyc_status: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();
        
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating user KYC status:', error);
      throw error;
    }
  }
  
  // Get KYC application status
  async getKYCApplication(userId) {
    try {
      const { data, error } = await supabase
        .from('kyc_applications')
        .select('*')
        .eq('user_id', userId)
        .single();
        
      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data;
    } catch (error) {
      console.error('Error fetching KYC application:', error);
      throw error;
    }
  }
  
  // Get KYC documents for a user
  async getKYCDocuments(userId) {
    try {
      const { data, error } = await supabase
        .from('kyc_documents')
        .select('*')
        .eq('user_id', userId);
        
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching KYC documents:', error);
      throw error;
    }
  }
  
  // Get comprehensive KYC status with steps
  async getKYCStatus(userId) {
    try {
      const [application, documents] = await Promise.all([
        this.getKYCApplication(userId),
        this.getKYCDocuments(userId)
      ]);

      const steps = [
        {
          id: 'personal_info',
          title: 'Personal Information',
          description: 'Basic personal details and contact information',
          status: application ? 'completed' : 'pending',
          completedAt: application?.created_at
        },
        {
          id: 'documents',
          title: 'Document Upload',
          description: 'Identity documents and proof of address',
          status: documents.length >= 4 ? 'completed' : documents.length > 0 ? 'in_progress' : 'pending',
          completedAt: documents.length >= 4 ? Math.max(...documents.map(d => new Date(d.created_at))) : null
        },
        {
          id: 'verification',
          title: 'Document Verification',
          description: 'Our team reviews your submitted documents',
          status: application?.status === 'approved' ? 'completed' :
                  application?.status === 'under_review' ? 'in_progress' :
                  application?.status === 'rejected' ? 'failed' : 'pending',
          completedAt: application?.reviewed_at
        }
      ];

      const overall = application?.status || 'not_started';

      return {
        overall,
        steps,
        application,
        documents
      };
    } catch (error) {
      console.error('Error getting KYC status:', error);
      throw error;
    }
  }

  // Admin functions
  async getAllKYCApplications() {
    try {
      const { data, error } = await supabase
        .from('kyc_applications')
        .select(`
          *,
          users (
            email,
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all KYC applications:', error);
      throw error;
    }
  }

  async updateKYCApplicationStatus(applicationId, status, adminNotes = '', reviewedBy = null) {
    try {
      const { data, error } = await supabase
        .from('kyc_applications')
        .update({
          status,
          admin_notes: adminNotes,
          reviewed_by: reviewedBy,
          reviewed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', applicationId)
        .select()
        .single();

      if (error) throw error;

      // Also update the user's KYC status
      if (data) {
        await this.updateUserKYCStatus(data.user_id, status);
      }

      return data;
    } catch (error) {
      console.error('Error updating KYC application status:', error);
      throw error;
    }
  }
}

export default new KYCService();
