import React, { useState, useEffect } from 'react';
import dbService from '../../services/dbService';
import { supabase } from '../../lib/supabase';
import '../styles/History.css';

const History = ({ user }) => {
  const [transactions, setTransactions] = useState([]);
  const [investments, setInvestments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState('30days');
  const [customDateRange, setCustomDateRange] = useState({
    start: '',
    end: ''
  });
  const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);
  const [stats, setStats] = useState({
    totalDeposited: 0,
    totalInvested: 0,
    totalEarnings: 0,
    avgMonthlyGrowth: 0
  });

  // Calculate date range based on filter
  const getDateRange = () => {
    const now = new Date();
    const start = new Date();

    switch (dateFilter) {
      case '7days':
        start.setDate(now.getDate() - 7);
        break;
      case '30days':
        start.setDate(now.getDate() - 30);
        break;
      case 'alltime':
        start.setFullYear(2020); // Far back date
        break;
      case 'custom':
        return {
          start: customDateRange.start ? new Date(customDateRange.start) : new Date(2020, 0, 1),
          end: customDateRange.end ? new Date(customDateRange.end) : now
        };
      default:
        start.setDate(now.getDate() - 30);
    }

    return { start, end: now };
  };

  // Filter transactions based on date range
  const getFilteredTransactions = () => {
    const { start, end } = getDateRange();
    return transactions.filter(transaction => {
      const transactionDate = new Date(transaction.created_at);
      return transactionDate >= start && transactionDate <= end;
    });
  };

  // Calculate statistics
  const calculateStats = () => {
    const filteredTransactions = getFilteredTransactions();
    const { start, end } = getDateRange();

    const deposits = filteredTransactions.filter(t => t.type === 'deposit');
    const earnings = filteredTransactions.filter(t => t.type === 'earning');

    const totalDeposited = deposits.reduce((sum, t) => sum + parseFloat(t.amount), 0);
    const totalEarnings = earnings.reduce((sum, t) => sum + parseFloat(t.amount), 0);

    // Calculate total invested from investments within date range
    const filteredInvestments = investments.filter(inv => {
      const investmentDate = new Date(inv.created_at);
      return investmentDate >= start && investmentDate <= end;
    });
    const totalInvested = filteredInvestments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);

    // Calculate average monthly growth
    const daysDiff = Math.max(1, (end - start) / (1000 * 60 * 60 * 24));
    const monthlyMultiplier = 30 / daysDiff;
    const avgMonthlyGrowth = totalInvested > 0 ? (totalEarnings * monthlyMultiplier / totalInvested) * 100 : 0;

    setStats({
      totalDeposited,
      totalInvested,
      totalEarnings,
      avgMonthlyGrowth
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        const authId = user.auth_id || user.id;
        const [userTransactions, userInvestments] = await Promise.all([
          dbService.getTransactions(authId, 100),
          dbService.getInvestments(authId)
        ]);

        setTransactions(userTransactions || []);
        setInvestments(userInvestments || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        setTransactions([]);
        setInvestments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up real-time subscription for new transactions
    if (user?.id) {
      const authId = user.auth_id || user.id;

      const subscription = supabase
        .channel('transaction_history')
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${authId}`
          },
          (payload) => {
            console.log('History - New transaction:', payload);
            fetchData(); // Refresh data
          }
        )
        .subscribe();

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [user]);

  // Recalculate stats when data or filter changes
  useEffect(() => {
    calculateStats();
  }, [transactions, investments, dateFilter, customDateRange]);

  const filteredTransactions = getFilteredTransactions();

  return (
    <div className="history">
      <div className="history-header">
        <h1>Transaction History</h1>
        <p>View all your transaction history and account activity</p>
      </div>

      {/* Statistics Cards */}
      <div className="history-stats">
        <div className="history-stat-card">
          <div className="stat-icon deposit-icon">💰</div>
          <div className="stat-content">
            <div className="stat-label">Amount Deposited</div>
            <div className="stat-value">${stats.totalDeposited.toLocaleString()}</div>
          </div>
        </div>
        <div className="history-stat-card">
          <div className="stat-icon investment-icon">📈</div>
          <div className="stat-content">
            <div className="stat-label">Total Invested</div>
            <div className="stat-value">${stats.totalInvested.toLocaleString()}</div>
          </div>
        </div>
        <div className="history-stat-card">
          <div className="stat-icon earnings-icon">💎</div>
          <div className="stat-content">
            <div className="stat-label">Total Earnings</div>
            <div className="stat-value">${stats.totalEarnings.toLocaleString()}</div>
          </div>
        </div>
        <div className="history-stat-card">
          <div className="stat-icon growth-icon">🚀</div>
          <div className="stat-content">
            <div className="stat-label">Avg Monthly Growth</div>
            <div className="stat-value">{stats.avgMonthlyGrowth.toFixed(2)}%</div>
          </div>
        </div>
      </div>

      {/* Date Filter */}
      <div className="history-controls">
        <div className="date-filter">
          <div className="date-filter-buttons">
            <button
              className={`filter-btn ${dateFilter === '7days' ? 'active' : ''}`}
              onClick={() => {
                setDateFilter('7days');
                setShowCustomDatePicker(false);
              }}
            >
              7 Days
            </button>
            <button
              className={`filter-btn ${dateFilter === '30days' ? 'active' : ''}`}
              onClick={() => {
                setDateFilter('30days');
                setShowCustomDatePicker(false);
              }}
            >
              30 Days
            </button>
            <button
              className={`filter-btn ${dateFilter === 'alltime' ? 'active' : ''}`}
              onClick={() => {
                setDateFilter('alltime');
                setShowCustomDatePicker(false);
              }}
            >
              All Time
            </button>
            <button
              className={`filter-btn ${dateFilter === 'custom' ? 'active' : ''}`}
              onClick={() => {
                setDateFilter('custom');
                setShowCustomDatePicker(!showCustomDatePicker);
              }}
            >
              Custom Range
            </button>
          </div>

          {showCustomDatePicker && (
            <div className="custom-date-picker">
              <div className="date-inputs">
                <div className="date-input-group">
                  <label>From</label>
                  <input
                    type="date"
                    value={customDateRange.start}
                    onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.target.value }))}
                    className="date-input"
                  />
                </div>
                <div className="date-input-group">
                  <label>To</label>
                  <input
                    type="date"
                    value={customDateRange.end}
                    onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.target.value }))}
                    className="date-input"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="history-content">
        {loading ? (
          <div className="history-loading">
            <div className="loading-spinner"></div>
            <span>Loading transactions...</span>
          </div>
        ) : (
          <div className="transaction-history-table">
            <div className="transaction-history-header">
              <div className="transaction-col">TYPE</div>
              <div className="transaction-col">DESCRIPTION</div>
              <div className="transaction-col">DATE</div>
              <div className="transaction-col">AMOUNT</div>
              <div className="transaction-col">STATUS</div>
            </div>
            <div className="transaction-history-body">
              {filteredTransactions.length === 0 ? (
                <div className="transaction-history-empty">
                  <p>No transactions found for the selected period</p>
                </div>
              ) : (
                filteredTransactions.map((transaction) => (
                  <div key={transaction.id} className="transaction-history-row">
                    <div className="transaction-col">
                      <div className={`transaction-type ${transaction.type}`}>
                        {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                      </div>
                    </div>
                    <div className="transaction-col">
                      <span>{transaction.description || `${transaction.type} transaction`}</span>
                    </div>
                    <div className="transaction-col">
                      {new Date(transaction.created_at).toLocaleDateString('en-US', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                    <div className="transaction-col">
                      <span className={`transaction-amount ${transaction.type === 'deposit' || transaction.type === 'earning' ? 'positive' : 'negative'}`}>
                        {transaction.type === 'deposit' || transaction.type === 'earning' ? '+' : '-'}${parseFloat(transaction.amount).toLocaleString()}
                      </span>
                    </div>
                    <div className="transaction-col">
                      <span className={`transaction-status ${transaction.status}`}>
                        {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
