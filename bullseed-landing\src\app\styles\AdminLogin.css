/* Professional BullSeed Admin Login Styles */
.admin-login {
  min-height: 100vh;
  background:
    radial-gradient(ellipse at top, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at bottom, rgba(0, 184, 148, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.admin-login::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 184, 148, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.admin-login-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
}

.admin-login-card {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%),
    rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 48px 40px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 5;
  pointer-events: auto;
  overflow: hidden;
}

.admin-login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.5), transparent);
}

.admin-login-header {
  text-align: center;
  margin-bottom: 32px;
}

.admin-login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.admin-logo-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.admin-login-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin: 0;
}

.admin-login-header p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-form-label {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.admin-form-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: white;
  font-size: 16px;
  transition: all 0.2s ease;
  position: relative;
  z-index: 5;
  pointer-events: auto;
}

.admin-form-input:focus {
  outline: none;
  border-color: #00d4aa;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.admin-form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.admin-error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #dc3545;
  font-size: 14px;
}

.admin-login-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.admin-login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.admin-login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-login-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.admin-login-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.admin-login-info {
  background: rgba(0, 212, 170, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.admin-login-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #00d4aa;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-login-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 0;
  font-family: 'Courier New', monospace;
}

.admin-login-info small {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.admin-security-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.admin-login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.admin-bg-pattern {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 184, 148, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .admin-login {
    padding: 16px;
  }
  
  .admin-login-card {
    padding: 24px;
  }
  
  .admin-login-header h1 {
    font-size: 24px;
  }
  
  .admin-logo-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }
  
  .admin-form-input {
    padding: 14px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .admin-login-btn {
    padding: 14px 20px;
  }
}

/* Security Warning Styles */
.admin-security-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 171, 0, 0.05) 100%);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffc107;
  font-size: 13px;
  margin-top: 16px;
  line-height: 1.4;
}

.admin-security-warning svg {
  flex-shrink: 0;
}

/* PWA Install Prompt */
.install-prompt {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 0;
  overflow: hidden;
  position: relative;
  z-index: 100;
}

.install-prompt-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
}

.install-prompt-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.install-prompt-text {
  flex: 1;
}

.install-prompt-text h3 {
  color: #00d4aa;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.install-prompt-text p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
}

.install-prompt-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.install-btn {
  background: #00d4aa;
  color: #0a0a0a;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 101;
  pointer-events: auto;
}

.install-btn:hover {
  background: #00b894;
  transform: translateY(-1px);
}

.dismiss-btn {
  background: transparent;
  color: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 101;
  pointer-events: auto;
}

.dismiss-btn:hover {
  color: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
}

.admin-security-warning strong {
  color: #ffeb3b;
}
