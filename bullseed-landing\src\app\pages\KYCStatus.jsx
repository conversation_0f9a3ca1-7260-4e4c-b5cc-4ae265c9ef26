import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import kycService from '../../services/kycService';
import '../styles/KYC.css';

const KYCStatus = ({ user }) => {
  const [kycStatus, setKycStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadKYCStatus();
  }, [user]);

  const loadKYCStatus = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError('');
      const status = await kycService.getKYCStatus(user.id);
      setKycStatus(status);
    } catch (error) {
      console.error('Error loading KYC status:', error);
      setError('Failed to load KYC status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
            <polyline points="22,4 12,14.01 9,11.01"/>
          </svg>
        );
      case 'pending':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <polyline points="12,6 12,12 16,14"/>
          </svg>
        );
      case 'rejected':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        );
      default:
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
        );
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#00d4aa';
      case 'pending':
        return '#f59e0b';
      case 'rejected':
        return '#dc2626';
      default:
        return '#6b7280';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="kyc-status">
        <div className="kyc-status-header">
          <h1>KYC Verification Status</h1>
          <p>Loading your verification status...</p>
        </div>
        <div className="kyc-loading">
          <div className="kyc-loading-spinner"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="kyc-status">
        <div className="kyc-status-header">
          <h1>KYC Verification Status</h1>
          <p>Error loading verification status</p>
        </div>
        <div className="kyc-error">
          <p>{error}</p>
          <button onClick={loadKYCStatus} className="kyc-retry-btn">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No KYC data
  if (!kycStatus) {
    return (
      <div className="kyc-status">
        <div className="kyc-status-header">
          <h1>KYC Verification Status</h1>
          <p>Start your identity verification process</p>
        </div>
        <div className="kyc-not-started">
          <p>You haven't started the KYC verification process yet.</p>
          <Link to="/app/kyc-application" className="kyc-action-btn primary">
            Start Verification
          </Link>
        </div>
      </div>
    );
  }

  const getOverallStatusMessage = () => {
    switch (kycStatus.overall) {
      case 'approved':
        return {
          title: 'KYC Verification Approved',
          message: 'Your identity has been successfully verified. You now have full access to all platform features.',
          color: '#00d4aa'
        };
      case 'pending':
        return {
          title: 'KYC Verification Pending',
          message: 'Your documents are being reviewed. This process typically takes 1-3 business days.',
          color: '#f59e0b'
        };
      case 'under_review':
        return {
          title: 'Under Review',
          message: 'Our team is currently reviewing your submitted documents.',
          color: '#3b82f6'
        };
      case 'rejected':
        return {
          title: 'KYC Verification Rejected',
          message: 'Some of your documents were rejected. Please review the feedback and resubmit.',
          color: '#dc2626'
        };
      default:
        return {
          title: 'KYC Verification Required',
          message: 'Complete your identity verification to unlock all platform features.',
          color: '#6b7280'
        };
    }
  };

  const overallStatus = getOverallStatusMessage();
  const completedSteps = kycStatus.steps.filter(step => step.status === 'completed').length;
  const totalSteps = kycStatus.steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <div className="kyc-status">
      <div className="kyc-status-header">
        <h1>KYC Verification Status</h1>
        <p>Track your identity verification progress</p>
      </div>

      <div className="kyc-status-content">
        <div className="kyc-overall-status">
          <div className="kyc-overall-card">
            <div className="kyc-overall-icon" style={{ color: overallStatus.color }}>
              {getStatusIcon(kycStatus.overall)}
            </div>
            <div className="kyc-overall-info">
              <h2 style={{ color: overallStatus.color }}>{overallStatus.title}</h2>
              <p>{overallStatus.message}</p>
            </div>
          </div>

          <div className="kyc-progress">
            <div className="kyc-progress-header">
              <span>Verification Progress</span>
              <span>{completedSteps}/{totalSteps} Steps Completed</span>
            </div>
            <div className="kyc-progress-bar">
              <div 
                className="kyc-progress-fill" 
                style={{ 
                  width: `${progressPercentage}%`,
                  backgroundColor: overallStatus.color 
                }}
              ></div>
            </div>
          </div>
        </div>

        <div className="kyc-steps">
          <h3>Verification Steps</h3>
          <div className="kyc-steps-list">
            {kycStatus.steps.map((step, index) => (
              <div key={step.id} className={`kyc-step ${step.status}`}>
                <div className="kyc-step-number">
                  <span>{index + 1}</span>
                </div>
                <div className="kyc-step-content">
                  <div className="kyc-step-header">
                    <h4>{step.title}</h4>
                    <div className="kyc-step-status">
                      <div 
                        className="kyc-step-status-icon"
                        style={{ color: getStatusColor(step.status) }}
                      >
                        {getStatusIcon(step.status)}
                      </div>
                      <span 
                        className="kyc-step-status-text"
                        style={{ color: getStatusColor(step.status) }}
                      >
                        {step.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <p className="kyc-step-description">{step.description}</p>
                  {step.completedAt && (
                    <div className="kyc-step-completed">
                      Completed on {new Date(step.completedAt).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="kyc-actions">
          {kycStatus.overall !== 'approved' && (
            <Link to="/app/kyc-application" className="kyc-action-btn primary">
              {kycStatus.overall === 'not_started' ? 'Start Verification' : 'Continue Verification'}
            </Link>
          )}

          <div className="kyc-help">
            <h4>Need Help?</h4>
            <p>If you have questions about the verification process, our support team is here to help.</p>
            <Link to="/contact" className="kyc-action-btn secondary">
              Contact Support
            </Link>
          </div>
        </div>

        <div className="kyc-info">
          <div className="kyc-info-card">
            <h4>Why do we need KYC verification?</h4>
            <ul>
              <li>Comply with regulatory requirements</li>
              <li>Protect your account from unauthorized access</li>
              <li>Enable higher transaction limits</li>
              <li>Provide better customer support</li>
            </ul>
          </div>
          
          <div className="kyc-info-card">
            <h4>What documents do I need?</h4>
            <ul>
              <li>Government-issued photo ID (passport, driver's license)</li>
              <li>Proof of address (utility bill, bank statement)</li>
              <li>Clear selfie with your ID document</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCStatus;
