// CoinGecko API service for real crypto market data
const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';
import bitcoinService from './bitcoinService.js';
import ethereumService from './ethereumService.js';
import bnbService from './bnbService.js';

class CryptoService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10000; // 10 seconds cache for real-time updates
    this.localStorageKey = 'bullseed_crypto_data';
    this.localStorageTimeout = 300000; // 5 minutes for localStorage cache
  }

  // Get cached data if available and not expired
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  // Set cache data
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });

    // Also save to localStorage for persistence across sessions
    if (key.startsWith('market-')) {
      this.saveToLocalStorage(data);
    }
  }

  // Save data to localStorage
  saveToLocalStorage(data) {
    try {
      const storageData = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(this.localStorageKey, JSON.stringify(storageData));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  // Get data from localStorage
  getFromLocalStorage() {
    try {
      const stored = localStorage.getItem(this.localStorageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Check if data is not too old (5 minutes)
        if (Date.now() - parsed.timestamp < this.localStorageTimeout) {
          return parsed.data;
        }
      }
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
    }
    return null;
  }

  // Fetch fresh data in background
  async fetchFreshData(coinIds, cacheKey) {
    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/coins/markets?vs_currency=usd&ids=${coinIds.join(',')}&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h`
      );

      if (response.ok) {
        const data = await response.json();
        const marketData = data.map((coin) => ({
          id: coin.id,
          symbol: `${coin.symbol.toUpperCase()}/USD`,
          name: coin.name,
          image: coin.image,
          price: coin.current_price,
          current_price: coin.current_price,
          change: coin.price_change_percentage_24h || 0,
          price_change_percentage_24h: coin.price_change_percentage_24h || 0,
          volume: this.formatVolume(coin.total_volume),
          marketCap: coin.market_cap || 0,
          market_cap: coin.market_cap || 0,
          market_cap_rank: coin.market_cap_rank
        }));

        this.setCachedData(cacheKey, marketData);
        console.log('Fresh data fetched and cached');
      }
    } catch (error) {
      console.warn('Background fetch failed:', error);
    }
  }

  // Fetch market data for specific coins with full details including images
  async getMarketData(coinIds = ['bitcoin', 'ethereum', 'cardano', 'polkadot', 'solana', 'chainlink', 'polygon', 'avalanche-2']) {
    const cacheKey = `market-${coinIds.join(',')}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    // If no cache, try localStorage for initial load
    const localStorageData = this.getFromLocalStorage();
    if (localStorageData) {
      console.log('Loading initial data from localStorage');
      // Set in memory cache too
      this.cache.set(cacheKey, {
        data: localStorageData,
        timestamp: Date.now()
      });
      // Still fetch fresh data in background
      this.fetchFreshData(coinIds, cacheKey);
      return localStorageData;
    }

    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/coins/markets?vs_currency=usd&ids=${coinIds.join(',')}&order=market_cap_desc&per_page=100&page=1&sparkline=false&price_change_percentage=24h`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform data to match our component format
      const marketData = data.map((coin) => {
        return {
          id: coin.id,
          symbol: `${coin.symbol.toUpperCase()}/USD`,
          name: coin.name,
          image: coin.image,
          price: coin.current_price,
          current_price: coin.current_price,
          change: coin.price_change_percentage_24h || 0,
          price_change_percentage_24h: coin.price_change_percentage_24h || 0,
          volume: this.formatVolume(coin.total_volume || 0),
          marketCap: coin.market_cap || 0,
          market_cap: coin.market_cap || 0,
          market_cap_rank: coin.market_cap_rank
        };
      });

      this.setCachedData(cacheKey, marketData);
      return marketData;
    } catch (error) {
      console.error('Error fetching market data:', error);

      // Try to get recent data from localStorage first
      const localStorageData = this.getFromLocalStorage();
      if (localStorageData) {
        console.log('Using recent data from localStorage');
        return localStorageData;
      }

      // Return fallback data if API fails and no localStorage data
      console.log('Using hardcoded fallback data');
      return this.getFallbackMarketData();
    }
  }

  // Get all available coins for selection
  async getAllCoins(limit = 100) {
    const cacheKey = `all-coins-${limit}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=${limit}&page=1&sparkline=false&price_change_percentage=24h`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      const allCoins = data.map((coin) => ({
        id: coin.id,
        symbol: coin.symbol.toUpperCase(),
        name: coin.name,
        image: coin.image,
        current_price: coin.current_price,
        price_change_percentage_24h: coin.price_change_percentage_24h || 0,
        market_cap: coin.market_cap || 0,
        market_cap_rank: coin.market_cap_rank
      }));

      this.setCachedData(cacheKey, allCoins);
      return allCoins;
    } catch (error) {
      console.error('Error fetching all coins:', error);
      return this.getFallbackAllCoins();
    }
  }

  // Get trending coins
  async getTrendingCoins() {
    const cacheKey = 'trending';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${COINGECKO_API_BASE}/search/trending`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const trending = data.coins.slice(0, 4).map(coin => ({
        symbol: `${coin.item.symbol}/USD`,
        name: coin.item.name,
        price: 0, // Trending endpoint doesn't include price
        change: 0,
        volume: 'N/A',
        rank: coin.item.market_cap_rank
      }));

      this.setCachedData(cacheKey, trending);
      return trending;
    } catch (error) {
      console.error('Error fetching trending coins:', error);
      return [];
    }
  }

  // Get global market stats
  async getGlobalStats() {
    const cacheKey = 'global-stats';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${COINGECKO_API_BASE}/global`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const stats = {
        totalMarketCap: data.data.total_market_cap.usd,
        total24hVolume: data.data.total_volume.usd,
        btcDominance: data.data.market_cap_percentage.bitcoin,
        ethDominance: data.data.market_cap_percentage.ethereum,
        activeCryptocurrencies: data.data.active_cryptocurrencies
      };

      this.setCachedData(cacheKey, stats);
      return stats;
    } catch (error) {
      console.error('Error fetching global stats:', error);
      return this.getFallbackGlobalStats();
    }
  }

  // Get specific coin data
  async getCoinData(coinId) {
    const cacheKey = `coin-${coinId}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(
        `${COINGECKO_API_BASE}/coins/${coinId}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const coinData = {
        id: data.id,
        symbol: data.symbol.toUpperCase(),
        name: data.name,
        image: data.image.large,
        currentPrice: data.market_data.current_price.usd,
        marketCap: data.market_data.market_cap.usd,
        marketCapRank: data.market_data.market_cap_rank,
        totalVolume: data.market_data.total_volume.usd,
        priceChange24h: data.market_data.price_change_24h,
        priceChangePercentage24h: data.market_data.price_change_percentage_24h,
        circulatingSupply: data.market_data.circulating_supply,
        totalSupply: data.market_data.total_supply,
        maxSupply: data.market_data.max_supply,
        ath: data.market_data.ath.usd,
        athDate: data.market_data.ath_date.usd,
        atl: data.market_data.atl.usd,
        atlDate: data.market_data.atl_date.usd
      };

      this.setCachedData(cacheKey, coinData);
      return coinData;
    } catch (error) {
      console.error(`Error fetching coin data for ${coinId}:`, error);
      return null;
    }
  }

  // Format volume numbers
  formatVolume(volume) {
    if (volume >= 1e9) {
      return `${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `${(volume / 1e6).toFixed(2)}M`;
    } else if (volume >= 1e3) {
      return `${(volume / 1e3).toFixed(2)}K`;
    }
    return volume.toFixed(2);
  }

  // Format price with appropriate decimal places
  formatPrice(price) {
    if (price >= 1000) {
      return price.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    } else if (price >= 1) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  }

  // Fallback data when API is unavailable
  getFallbackMarketData() {
    return [
      {
        id: 'bitcoin',
        symbol: 'BTC/USD',
        name: 'Bitcoin',
        image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        price: 118080,
        current_price: 118080,
        change: 1.59,
        price_change_percentage_24h: 1.59,
        volume: '1.12B',
        marketCap: 2300000000000,
        market_cap: 2300000000000,
        market_cap_rank: 1
      },
      {
        id: 'ethereum',
        symbol: 'ETH/USD',
        name: 'Ethereum',
        image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
        price: 4100,
        current_price: 4100,
        change: 2.15,
        price_change_percentage_24h: 2.15,
        volume: '174.11M',
        marketCap: 490000000000,
        market_cap: 490000000000,
        market_cap_rank: 2
      },
      {
        id: 'cardano',
        symbol: 'ADA/USD',
        name: 'Cardano',
        image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
        price: 0.4567,
        current_price: 0.4567,
        change: 2.34,
        price_change_percentage_24h: 2.34,
        volume: '87.11M',
        marketCap: 16000000000,
        market_cap: 16000000000,
        market_cap_rank: 8
      },
      {
        id: 'solana',
        symbol: 'SOL/USD',
        name: 'Solana',
        image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
        price: 145.67,
        current_price: 145.67,
        change: 4.1,
        price_change_percentage_24h: 4.1,
        volume: '45.23M',
        marketCap: 68000000000,
        market_cap: 68000000000,
        market_cap_rank: 5
      },
      {
        id: 'chainlink',
        symbol: 'LINK/USD',
        name: 'Chainlink',
        image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png',
        price: 14.23,
        current_price: 14.23,
        change: 1.8,
        price_change_percentage_24h: 1.8,
        volume: '25.45M',
        marketCap: 8500000000,
        market_cap: 8500000000,
        market_cap_rank: 15
      }
    ];
  }

  getFallbackAllCoins() {
    return this.getFallbackMarketData().concat([
      {
        id: 'polkadot',
        symbol: 'DOT',
        name: 'Polkadot',
        image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png',
        current_price: 7.89,
        price_change_percentage_24h: -0.56,
        market_cap: 9500000000,
        market_cap_rank: 12
      },
      {
        id: 'polygon',
        symbol: 'MATIC',
        name: 'Polygon',
        image: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
        current_price: 0.89,
        price_change_percentage_24h: 2.1,
        market_cap: 8200000000,
        market_cap_rank: 16
      },
      {
        id: 'avalanche-2',
        symbol: 'AVAX',
        name: 'Avalanche',
        image: 'https://assets.coingecko.com/coins/images/12559/large/avalanche-avax-logo.png',
        current_price: 35.67,
        price_change_percentage_24h: 3.2,
        market_cap: 14000000000,
        market_cap_rank: 10
      },
      {
        id: 'binancecoin',
        symbol: 'BNB',
        name: 'BNB',
        image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
        current_price: 635.45,
        price_change_percentage_24h: 1.8,
        market_cap: 92000000000,
        market_cap_rank: 4
      },
      {
        id: 'ripple',
        symbol: 'XRP',
        name: 'XRP',
        image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png',
        current_price: 0.52,
        price_change_percentage_24h: -0.8,
        market_cap: 29000000000,
        market_cap_rank: 6
      },
      {
        id: 'dogecoin',
        symbol: 'DOGE',
        name: 'Dogecoin',
        image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png',
        current_price: 0.078,
        price_change_percentage_24h: 2.5,
        market_cap: 11000000000,
        market_cap_rank: 11
      },
      {
        id: 'tron',
        symbol: 'TRX',
        name: 'TRON',
        image: 'https://assets.coingecko.com/coins/images/1094/large/tron-logo.png',
        current_price: 0.165,
        price_change_percentage_24h: 1.2,
        market_cap: 14500000000,
        market_cap_rank: 9
      },
      {
        id: 'litecoin',
        symbol: 'LTC',
        name: 'Litecoin',
        image: 'https://assets.coingecko.com/coins/images/2/large/litecoin.png',
        current_price: 89.45,
        price_change_percentage_24h: 0.9,
        market_cap: 6700000000,
        market_cap_rank: 17
      }
    ]);
  }

  getFallbackGlobalStats() {
    return {
      totalMarketCap: 2400000000000,
      total24hVolume: 89000000000,
      btcDominance: 54.2,
      ethDominance: 17.8,
      activeCryptocurrencies: 2500
    };
  }

  // Start real-time updates (polling) with faster intervals
  startRealTimeUpdates(callback, interval = 5000) { // Update every 5 seconds for real-time feel
    const updateData = async () => {
      try {
        // Clear cache to ensure fresh data
        this.clearCache();
        const marketData = await this.getMarketData();
        callback(marketData);
      } catch (error) {
        console.error('Error in real-time update:', error);
        // Continue with cached data if available
        const cachedData = this.getCachedData('market-bitcoin,ethereum,cardano,polkadot,solana,chainlink,polygon,avalanche-2');
        if (cachedData) {
          callback(cachedData);
        }
      }
    };

    // Initial update
    updateData();

    // Set up polling with faster interval
    const intervalId = setInterval(updateData, interval);

    // Return cleanup function
    return () => clearInterval(intervalId);
  }

  // Clear all cached data to force fresh fetches
  clearCache() {
    Object.keys(this.cache).forEach(key => {
      delete this.cache[key];
    });
  }

  // Start high-frequency updates for ticker (even faster)
  startTickerUpdates(callback, interval = 3000) { // Update every 3 seconds for ticker
    const updateData = async () => {
      try {
        // Clear cache to ensure fresh data
        this.clearCache();
        const marketData = await this.getMarketData();
        callback(marketData);
      } catch (error) {
        console.error('Error in ticker update:', error);
      }
    };

    // Initial update
    updateData();

    // Set up high-frequency polling
    const intervalId = setInterval(updateData, interval);

    // Return cleanup function
    return () => clearInterval(intervalId);
  }

  // Crypto deposit functionality - Bitcoin, Ethereum, and BNB Smart Chain
  getSupportedCryptocurrencies() {
    return [
      {
        id: 'bitcoin',
        symbol: 'BTC',
        name: 'Bitcoin',
        network: 'Bitcoin Network',
        icon: '₿',
        color: '#f7931a',
        minDeposit: 0.001,
        confirmations: 3,
        address: '******************************************'
      },
      {
        id: 'ethereum',
        symbol: 'ETH',
        name: 'Ethereum',
        network: 'Ethereum Network',
        icon: '⟠',
        color: '#627eea',
        minDeposit: 0.01,
        confirmations: 12,
        address: '******************************************'
      },
      {
        id: 'bnb',
        symbol: 'BNB',
        name: 'BNB Smart Chain',
        network: 'BNB Smart Chain',
        icon: '◆',
        color: '#f3ba2f',
        minDeposit: 0.1,
        confirmations: 15,
        address: '******************************************'
      }
    ];
  }

  // Convert USD to crypto amount
  async convertUsdToCrypto(usdAmount, cryptoId) {
    try {
      let conversion;

      switch (cryptoId) {
        case 'bitcoin':
          conversion = await bitcoinService.convertUsdToBitcoin(usdAmount);
          return {
            cryptoAmount: conversion.btcAmount,
            cryptoPrice: conversion.btcPrice,
            formattedAmount: this.formatCryptoAmount(conversion.btcAmount, cryptoId)
          };

        case 'ethereum':
          conversion = await ethereumService.convertUsdToEthereum(usdAmount);
          return {
            cryptoAmount: conversion.ethAmount,
            cryptoPrice: conversion.ethPrice,
            formattedAmount: this.formatCryptoAmount(conversion.ethAmount, cryptoId)
          };

        case 'bnb':
          conversion = await bnbService.convertUsdToBnb(usdAmount);
          return {
            cryptoAmount: conversion.bnbAmount,
            cryptoPrice: conversion.bnbPrice,
            formattedAmount: this.formatCryptoAmount(conversion.bnbAmount, cryptoId)
          };

        default:
          throw new Error(`Unsupported cryptocurrency: ${cryptoId}`);
      }
    } catch (error) {
      console.error('Error converting USD to crypto:', error);
      throw error;
    }
  }

  // Format crypto amount with appropriate decimal places
  formatCryptoAmount(amount, cryptoId) {
    const crypto = this.getSupportedCryptocurrencies().find(c => c.id === cryptoId);
    if (!crypto) return amount.toFixed(8);

    // Different formatting for different cryptocurrencies
    switch (crypto.symbol) {
      case 'BTC':
        return amount.toFixed(8);
      case 'ETH':
        return amount.toFixed(6);
      case 'BNB':
        return amount.toFixed(6);
      case 'USDT':
      case 'USDC':
        return amount.toFixed(2);
      default:
        return amount.toFixed(8);
    }
  }

  // Get real crypto deposit address
  generateDepositAddress(cryptoId, userId) {
    const crypto = this.getSupportedCryptocurrencies().find(c => c.id === cryptoId);
    if (!crypto) throw new Error('Unsupported cryptocurrency');

    // Return the real address for the selected cryptocurrency
    return crypto.address;
  }

  // Simple hash function for demo addresses
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(40, '0');
  }

  // Get network fee estimate
  async getNetworkFee(cryptoId) {
    // In production, fetch real network fees from blockchain APIs
    const fees = {
      'bitcoin': { slow: 0.0001, standard: 0.0003, fast: 0.0005 },
      'ethereum': { slow: 0.002, standard: 0.005, fast: 0.01 },
      'tether': { slow: 0.002, standard: 0.005, fast: 0.01 },
      'usd-coin': { slow: 0.002, standard: 0.005, fast: 0.01 }
    };

    return fees[cryptoId] || { slow: 0.001, standard: 0.003, fast: 0.005 };
  }
}

export default new CryptoService();
