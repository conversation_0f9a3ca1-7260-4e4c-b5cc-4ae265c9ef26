<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BullSeed - Crypto Investment Platform</title>

    <!-- Cache busting meta tags -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="version" content="3.0.0" />
    <meta name="mobile-web-app-capable" content="no" />
    <meta name="apple-mobile-web-app-capable" content="no" />

    <!-- Basic manifest for icon references only -->
    <link rel="manifest" href="/manifest.json?v=2" />


  </head>
  <body>
    <div id="root"></div>

    <!-- Aggressive cache clearing for Chrome mobile -->
    <script>
      // Version-based cache busting
      const CACHE_VERSION = 'v3.0.0';
      const FORCE_RELOAD_KEY = 'bullseed_force_reload_' + CACHE_VERSION;

      (function() {
        // Check if we need to force reload for this version
        const hasReloaded = localStorage.getItem(FORCE_RELOAD_KEY);

        if (!hasReloaded) {
          console.log('Forcing cache clear and reload for version:', CACHE_VERSION);

          // Clear all storage
          try {
            localStorage.clear();
            sessionStorage.clear();
          } catch(e) {}

          // Replace service worker with empty one, then unregister
          if ('serviceWorker' in navigator) {
            // First register empty service worker to replace cached one
            navigator.serviceWorker.register('/sw.js').then(function(registration) {
              console.log('Empty service worker registered');

              // Then unregister all service workers
              return navigator.serviceWorker.getRegistrations();
            }).then(function(registrations) {
              registrations.forEach(function(registration) {
                registration.unregister();
              });

              // Clear caches after service worker cleanup
              if ('caches' in window) {
                caches.keys().then(function(cacheNames) {
                  Promise.all(
                    cacheNames.map(function(cacheName) {
                      return caches.delete(cacheName);
                    })
                  ).then(function() {
                    // Mark as reloaded and force refresh
                    localStorage.setItem(FORCE_RELOAD_KEY, 'true');
                    window.location.reload(true);
                  });
                });
              } else {
                localStorage.setItem(FORCE_RELOAD_KEY, 'true');
                window.location.reload(true);
              }
            });
          } else {
            // No service worker support, just clear caches
            if ('caches' in window) {
              caches.keys().then(function(cacheNames) {
                Promise.all(
                  cacheNames.map(function(cacheName) {
                    return caches.delete(cacheName);
                  })
                ).then(function() {
                  localStorage.setItem(FORCE_RELOAD_KEY, 'true');
                  window.location.reload(true);
                });
              });
            } else {
              localStorage.setItem(FORCE_RELOAD_KEY, 'true');
              window.location.reload(true);
            }
          }

          return; // Don't continue loading
        }
      })();
    </script>

    <script type="module" src="/src/main.jsx"></script>


  </body>
</html>
