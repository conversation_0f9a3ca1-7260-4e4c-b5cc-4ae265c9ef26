.referrals-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
}

.referrals-header {
  text-align: center;
  margin-bottom: 32px;
}

.referrals-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.referrals-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
}

.referrals-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 170, 0.3);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Referral Link Section */
.referral-link-section {
  margin-bottom: 32px;
}

.referral-link-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.referral-link-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.referral-link-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.referral-commission-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
}

.commission-rate {
  font-size: 1.5rem;
  font-weight: 700;
  color: #00d4aa;
}

.commission-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.referral-link-container {
  margin-bottom: 20px;
}

.referral-link-input {
  display: flex;
  gap: 12px;
  align-items: center;
}

.referral-link-field {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.copy-button {
  background: #00d4aa;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.copy-button:hover {
  background: #00b894;
  transform: translateY(-1px);
}

.copy-button.copied {
  background: #10b981;
}

.share-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.share-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.share-button.twitter {
  background: #1da1f2;
  color: white;
}

.share-button.facebook {
  background: #4267b2;
  color: white;
}

.share-button:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* Stats Grid */
.referral-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.stat-icon {
  background: rgba(0, 212, 170, 0.1);
  border-radius: 12px;
  padding: 12px;
  color: #00d4aa;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Tabs */
.referral-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button.active {
  background: #00d4aa;
  color: white;
}

.tab-button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

/* Tab Content */
.tab-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

/* Overview Content */
.referral-program-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: white;
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.step-number {
  background: #00d4aa;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: white;
}

.step-content p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

/* Earnings Table */
.earnings-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 80px 100px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 80px 100px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  align-items: center;
}

.table-col {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.table-empty {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.investment-type {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.commission-amount {
  color: #00d4aa;
  font-weight: 600;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-completed {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.level-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

/* Program Details */
.program-details h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: white;
}

.level-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.level-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.level-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.level-rate {
  font-size: 1.5rem;
  font-weight: 700;
  color: #00d4aa;
}

.level-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: white;
}

.level-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.program-terms {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
}

.program-terms h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: white;
}

.program-terms ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.program-terms li {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.program-terms li::before {
  content: '•';
  color: #00d4aa;
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .referrals-page {
    padding: 16px;
  }
  
  .referral-link-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .referral-link-input {
    flex-direction: column;
  }
  
  .share-buttons {
    justify-content: center;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-col {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .table-col::before {
    content: attr(data-label);
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
  }
  
  .steps {
    grid-template-columns: 1fr;
  }
  
  .level-cards {
    grid-template-columns: 1fr;
  }
}
