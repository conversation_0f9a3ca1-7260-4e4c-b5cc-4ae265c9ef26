{"buildCommand": "npm run build", "outputDirectory": "dist", "rewrites": [{"source": "/((?!api|_next|_static|favicon.ico|logo192.png|logo512.png|BullSeed_LOGO.png|BullSeed_Outline.png|Captain_Apeshit.png|Pixel_Fever.png|Suzi.png|img\\ 1.png|img\\ 2.png|vite.svg|assets/).*)", "destination": "/index.html"}], "headers": [{"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}