{"buildCommand": "npm run build", "outputDirectory": "dist", "rewrites": [{"source": "/((?!api|_next|_static|favicon.ico|manifest.json|sw.js|logo192.png|logo512.png|BullSeed_LOGO.png|BullSeed_Outline.png|Captain_Apeshit.png|Pixel_Fever.png|Suzi.png|img\\ 1.png|img\\ 2.png|vite.svg|assets/).*)", "destination": "/index.html"}], "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}