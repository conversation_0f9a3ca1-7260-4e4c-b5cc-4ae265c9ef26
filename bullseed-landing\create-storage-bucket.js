// Script to create profile-images storage bucket
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://fezbbwqawjrvzrjqgyhw.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // You'll need to set this

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createProfileImagesBucket() {
  try {
    // Create the bucket
    const { data, error } = await supabase.storage.createBucket('profile-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    });

    if (error) {
      console.error('Error creating bucket:', error);
    } else {
      console.log('Bucket created successfully:', data);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

createProfileImagesBucket();
