import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseServiceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables for admin client');
}

// Admin client with service role key - bypasses RLS
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Admin database service functions
export const adminDbService = {
  // Deposit functions
  async confirmDeposit(depositId, depositAmount, userId) {
    try {
      // Start a transaction-like operation

      // 1. Get deposit details first to get cryptocurrency info
      const { data: depositData, error: depositFetchError } = await supabaseAdmin
        .from('crypto_deposits')
        .select('cryptocurrency')
        .eq('id', depositId)
        .single();

      if (depositFetchError) throw depositFetchError;

      // 2. Update deposit status
      const { error: depositError } = await supabaseAdmin
        .from('crypto_deposits')
        .update({
          status: 'confirmed',
          confirmed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', depositId);

      if (depositError) throw depositError;

      // 3. Get current user data (including auth_id)
      const { data: userData, error: fetchError } = await supabaseAdmin
        .from('users')
        .select('balance, auth_id')
        .eq('id', userId)
        .single();

      if (fetchError) throw fetchError;

      const currentBalance = userData?.balance || 0;
      const newBalance = currentBalance + depositAmount;

      // 4. Update user balance
      const { error: balanceError } = await supabaseAdmin
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (balanceError) throw balanceError;

      // 5. Create transaction record using auth_id (to match Dashboard queries)
      const cryptoName = depositData.cryptocurrency === 'BTC' ? 'Bitcoin' : depositData.cryptocurrency;
      const { error: transactionError } = await supabaseAdmin
        .from('transactions')
        .insert({
          user_id: userData.auth_id, // Use auth_id instead of id
          type: 'deposit',
          amount: depositAmount,
          status: 'completed',
          description: `${cryptoName} Deposit`,
          created_at: new Date().toISOString()
        });

      if (transactionError) throw transactionError;

      return { success: true };

    } catch (error) {
      console.error('Error in confirmDeposit:', error);
      throw error;
    }
  },

  // Reject deposit
  async rejectDeposit(depositId) {
    try {
      const { error } = await supabaseAdmin
        .from('crypto_deposits')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', depositId);

      if (error) throw error;
      return { success: true };

    } catch (error) {
      console.error('Error in rejectDeposit:', error);
      throw error;
    }
  },

  // Get all deposits (admin view)
  async getAllDeposits(limit = 50) {
    try {
      const { data, error } = await supabaseAdmin
        .from('crypto_deposits')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllDeposits:', error);
      throw error;
    }
  },

  // Get all withdrawals (admin view)
  async getAllWithdrawals(limit = 50) {
    try {
      const { data, error } = await supabaseAdmin
        .from('crypto_withdrawals')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllWithdrawals:', error);
      throw error;
    }
  },

  // Approve withdrawal
  async approveWithdrawal(withdrawalId, transactionHash = null, adminId = null) {
    try {
      // 1. Get withdrawal details
      const { data: withdrawalData, error: withdrawalError } = await supabaseAdmin
        .from('crypto_withdrawals')
        .select('*')
        .eq('id', withdrawalId)
        .single();

      if (withdrawalError) throw withdrawalError;
      if (!withdrawalData) throw new Error('Withdrawal not found');

      // 2. Update withdrawal status
      const updateData = {
        status: 'completed',
        processed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (transactionHash) {
        updateData.transaction_hash = transactionHash;
      }

      if (adminId) {
        updateData.processed_by = adminId;
      }

      const { error: updateError } = await supabaseAdmin
        .from('crypto_withdrawals')
        .update(updateData)
        .eq('id', withdrawalId);

      if (updateError) throw updateError;

      // 3. Update pending transaction to completed
      const { error: transactionUpdateError } = await supabaseAdmin
        .from('transactions')
        .update({
          status: 'completed',
          description: `${cryptoName} Withdrawal - ${withdrawalData.crypto_amount} ${withdrawalData.cryptocurrency}`,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', withdrawalData.user_id)
        .eq('type', 'withdrawal')
        .eq('amount', -withdrawalData.amount_usd)
        .eq('status', 'pending');

      if (transactionUpdateError) {
        console.error('Error updating transaction:', transactionUpdateError);
        // Don't fail approval for transaction update error
      }

      // Note: Balance was already deducted when withdrawal was created, so no balance update needed here

      return { success: true };

    } catch (error) {
      console.error('Error in approveWithdrawal:', error);
      throw error;
    }
  },

  // Reject withdrawal
  async rejectWithdrawal(withdrawalId, adminNotes = null, adminId = null) {
    try {
      // 1. Get withdrawal details
      const { data: withdrawalData, error: withdrawalError } = await supabaseAdmin
        .from('crypto_withdrawals')
        .select('*')
        .eq('id', withdrawalId)
        .single();

      if (withdrawalError) throw withdrawalError;
      if (!withdrawalData) throw new Error('Withdrawal not found');

      // 2. Update withdrawal status
      const updateData = {
        status: 'failed',
        processed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (adminNotes) {
        updateData.admin_notes = adminNotes;
      }

      if (adminId) {
        updateData.processed_by = adminId;
      }

      const { error: updateError } = await supabaseAdmin
        .from('crypto_withdrawals')
        .update(updateData)
        .eq('id', withdrawalId);

      if (updateError) throw updateError;

      // 3. Refund the balance (since it was deducted when withdrawal was created)
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .select('balance')
        .eq('auth_id', withdrawalData.user_id)
        .single();

      if (userError) throw userError;
      if (!userData) throw new Error('User not found');

      const currentBalance = userData.balance || 0;
      const refundedBalance = currentBalance + withdrawalData.amount_usd;

      // Refund the balance
      const { error: balanceError } = await supabaseAdmin
        .from('users')
        .update({
          balance: refundedBalance,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', withdrawalData.user_id);

      if (balanceError) throw balanceError;

      // 4. Update pending transaction to failed
      const cryptoName = withdrawalData.cryptocurrency === 'BTC' ? 'Bitcoin' : withdrawalData.cryptocurrency;
      const { error: transactionUpdateError } = await supabaseAdmin
        .from('transactions')
        .update({
          status: 'failed',
          description: `${cryptoName} Withdrawal (Rejected) - ${withdrawalData.crypto_amount} ${withdrawalData.cryptocurrency}`,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', withdrawalData.user_id)
        .eq('type', 'withdrawal')
        .eq('amount', -withdrawalData.amount_usd)
        .eq('status', 'pending');

      if (transactionUpdateError) {
        console.error('Error updating transaction:', transactionUpdateError);
        // Don't fail rejection for transaction update error
      }

      return { success: true };

    } catch (error) {
      console.error('Error in rejectWithdrawal:', error);
      throw error;
    }
  },

  // Get all users (admin view)
  async getAllUsers(limit = 100) {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllUsers:', error);
      throw error;
    }
  },

  // Get all transactions (admin view)
  async getAllTransactions(limit = 100) {
    try {
      const { data, error } = await supabaseAdmin
        .from('transactions')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllTransactions:', error);
      throw error;
    }
  },

  // Update user balance (admin function)
  async updateUserBalance(userId, newBalance) {
    try {
      const { error } = await supabaseAdmin
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;
      return { success: true };

    } catch (error) {
      console.error('Error in updateUserBalance:', error);
      throw error;
    }
  }
};
