import React, { useState, useEffect } from 'react';
import userSettingsService from '../../services/userSettingsService';
import { useUserPreferences } from '../../contexts/UserPreferencesContext';
import { formattingService } from '../../services/formattingService';
import { supabase } from '../../lib/supabase';
import '../styles/AccountSettings.css';

const AccountSettings = ({ user }) => {
  const { preferences, updatePreferences: updateUserPreferences } = useUserPreferences();
  const [activeTab, setActiveTab] = useState('security');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [settings, setSettings] = useState({
    twoFactorEnabled: false,
    emailNotifications: true,
    smsNotifications: false,
    marketingEmails: true,
    loginAlerts: true
  });

  const [localPreferences, setLocalPreferences] = useState({
    language: 'en',
    timezone: 'America/New_York',
    currency: 'USD'
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [sessions, setSessions] = useState([]);
  const [passwordErrors, setPasswordErrors] = useState([]);

  // Load user settings on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserSettings();
      loadUserSessions();
    }
  }, [user]);

  // Sync local preferences with context preferences
  useEffect(() => {
    if (!preferences.loading) {
      setLocalPreferences({
        language: preferences.language,
        timezone: preferences.timezone,
        currency: preferences.currency
      });
    }
  }, [preferences]);

  const loadUserSettings = async () => {
    try {
      setLoading(true);
      const userSettings = await userSettingsService.getUserSettings(user.id);

      setSettings({
        twoFactorEnabled: userSettings.twoFactorEnabled,
        emailNotifications: userSettings.emailNotifications,
        smsNotifications: userSettings.smsNotifications,
        marketingEmails: userSettings.marketingEmails,
        loginAlerts: userSettings.loginAlerts
      });

      setLocalPreferences({
        language: userSettings.language,
        timezone: userSettings.timezone,
        currency: userSettings.currency
      });
    } catch (error) {
      console.error('Error loading user settings:', error);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const loadUserSessions = async () => {
    try {
      const userSessions = await userSettingsService.getUserSessions(user.id);
      setSessions(userSessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  };

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  const handleSettingChange = async (setting, value) => {
    clearMessages();
    setSaving(true);

    try {
      const result = await userSettingsService.updateNotificationSettings(user.id, {
        [setting]: value
      });

      if (result.success) {
        setSettings(prev => ({
          ...prev,
          [setting]: value
        }));
        setSuccess('Settings updated successfully');
      } else {
        setError(result.error || 'Failed to update settings');
      }
    } catch (error) {
      setError('Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  const handlePreferenceChange = async (preference, value) => {
    clearMessages();
    setSaving(true);

    try {
      // Update both local state and context
      const result = await updateUserPreferences({
        [preference]: value
      });

      if (result.success) {
        setLocalPreferences(prev => ({
          ...prev,
          [preference]: value
        }));
        setSuccess('Preferences updated successfully');

        // Show immediate feedback that the change is applied
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      } else {
        setError(result.error || 'Failed to update preferences');
      }
    } catch (error) {
      setError('Failed to update preferences');
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear password errors when user starts typing
    if (passwordErrors.length > 0) {
      setPasswordErrors([]);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    clearMessages();

    // Validate passwords match
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    // Validate password strength
    const validation = userSettingsService.validatePassword(passwordForm.newPassword);
    if (!validation.isValid) {
      setPasswordErrors(validation.errors);
      return;
    }

    setSaving(true);

    try {
      const result = await userSettingsService.changePassword(
        passwordForm.currentPassword,
        passwordForm.newPassword
      );

      if (result.success) {
        setSuccess('Password changed successfully');
        setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      } else {
        setError(result.error || 'Failed to change password');
      }
    } catch (error) {
      setError('Failed to change password');
    } finally {
      setSaving(false);
    }
  };

  const handleEnable2FA = async () => {
    clearMessages();
    setSaving(true);

    try {
      const result = await userSettingsService.toggle2FA(user.id, !settings.twoFactorEnabled);

      if (result.success) {
        setSettings(prev => ({ ...prev, twoFactorEnabled: !prev.twoFactorEnabled }));
        setSuccess(`Two-factor authentication ${!settings.twoFactorEnabled ? 'enabled' : 'disabled'} successfully`);
      } else {
        setError(result.error || 'Failed to update 2FA settings');
      }
    } catch (error) {
      setError('Failed to update 2FA settings');
    } finally {
      setSaving(false);
    }
  };

  const handleRevokeSession = async (sessionId) => {
    clearMessages();

    try {
      const result = await userSettingsService.revokeSession(sessionId);

      if (result.success) {
        setSessions(prev => prev.filter(session => session.id !== sessionId));
        setSuccess('Session revoked successfully');
      } else {
        setError(result.error || 'Failed to revoke session');
      }
    } catch (error) {
      setError('Failed to revoke session');
    }
  };

  const handleExportData = async () => {
    clearMessages();

    try {
      const result = await userSettingsService.exportAccountData(user.id);

      if (result.success) {
        setSuccess('Account data exported successfully');
      } else {
        setError(result.error || 'Failed to export account data');
      }
    } catch (error) {
      setError('Failed to export account data');
    }
  };

  const handleDeleteAccount = async () => {
    const password = prompt('Please enter your password to confirm account deletion:');
    if (!password) return;

    const confirmed = confirm('Are you sure you want to delete your account? This action cannot be undone.');
    if (!confirmed) return;

    clearMessages();
    setSaving(true);

    try {
      const result = await userSettingsService.deleteAccount(user.id, password);

      if (result.success) {
        alert('Account deleted successfully. You will be redirected to the homepage.');
        window.location.href = '/';
      } else {
        setError(result.error || 'Failed to delete account');
      }
    } catch (error) {
      setError('Failed to delete account');
    } finally {
      setSaving(false);
    }
  };

  const renderSecurityTab = () => (
    <div className="settings-tab-content">
      {error && (
        <div className="settings-error">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          {error}
        </div>
      )}

      {success && (
        <div className="settings-success">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
          {success}
        </div>
      )}

      <div className="settings-section">
        <h3>Password</h3>
        <p>Update your password to keep your account secure</p>
        <form onSubmit={handlePasswordSubmit} className="password-form">
          <div className="settings-form-group">
            <label>Current Password</label>
            <input
              type="password"
              value={passwordForm.currentPassword}
              onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
              placeholder="Enter current password"
              required
              disabled={saving}
            />
          </div>
          <div className="settings-form-group">
            <label>New Password</label>
            <input
              type="password"
              value={passwordForm.newPassword}
              onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
              placeholder="Enter new password"
              required
              disabled={saving}
            />
            {passwordErrors.length > 0 && (
              <div className="password-errors">
                {passwordErrors.map((error, index) => (
                  <div key={index} className="password-error">{error}</div>
                ))}
              </div>
            )}
          </div>
          <div className="settings-form-group">
            <label>Confirm New Password</label>
            <input
              type="password"
              value={passwordForm.confirmPassword}
              onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
              placeholder="Confirm new password"
              required
              disabled={saving}
            />
          </div>
          <button type="submit" className="settings-btn primary" disabled={saving}>
            {saving ? 'Updating...' : 'Update Password'}
          </button>
        </form>
      </div>

      <div className="settings-section">
        <h3>Two-Factor Authentication</h3>
        <p>Add an extra layer of security to your account</p>
        <div className="settings-2fa">
          <div className="settings-2fa-status">
            <div className="settings-2fa-icon">
              {settings.twoFactorEnabled ? (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  <path d="M9 12l2 2 4-4"/>
                </svg>
              ) : (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                </svg>
              )}
            </div>
            <div className="settings-2fa-info">
              <h4>{settings.twoFactorEnabled ? 'Enabled' : 'Disabled'}</h4>
              <p>
                {settings.twoFactorEnabled 
                  ? 'Your account is protected with 2FA'
                  : 'Secure your account with 2FA'
                }
              </p>
            </div>
          </div>
          <button
            className={`settings-btn ${settings.twoFactorEnabled ? 'secondary' : 'primary'}`}
            onClick={handleEnable2FA}
            disabled={saving}
          >
            {saving ? 'Updating...' : (settings.twoFactorEnabled ? 'Disable 2FA' : 'Enable 2FA')}
          </button>
        </div>
      </div>

      <div className="settings-section">
        <h3>Login Sessions</h3>
        <p>Manage your active login sessions</p>
        <div className="settings-sessions">
          {sessions.map((session) => (
            <div key={session.id} className="settings-session">
              <div className="settings-session-info">
                <h4>{session.isCurrent ? 'Current Session' : 'Session'}</h4>
                <p>{session.device} • {session.location}</p>
                <span className="settings-session-time">{session.lastActive}</span>
              </div>
              {session.isCurrent ? (
                <span className="settings-session-current">Current</span>
              ) : (
                <button
                  className="settings-session-revoke"
                  onClick={() => handleRevokeSession(session.id)}
                >
                  Revoke
                </button>
              )}
            </div>
          ))}
          {sessions.length === 0 && (
            <div className="settings-sessions-empty">
              <p>No active sessions found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="settings-tab-content">
      {error && (
        <div className="settings-error">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          {error}
        </div>
      )}

      {success && (
        <div className="settings-success">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
          {success}
        </div>
      )}

      <div className="settings-section">
        <h3>Email Notifications</h3>
        <p>Choose what email notifications you'd like to receive</p>
        <div className="settings-toggles">
          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Account Activity</h4>
              <p>Login alerts, password changes, and security updates</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.loginAlerts}
                onChange={(e) => handleSettingChange('loginAlerts', e.target.checked)}
                disabled={saving}
              />
              <span className="settings-slider"></span>
            </label>
          </div>

          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Investment Updates</h4>
              <p>Daily returns, investment completions, and portfolio updates</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                disabled={saving}
              />
              <span className="settings-slider"></span>
            </label>
          </div>

          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Marketing Communications</h4>
              <p>Product updates, new features, and promotional offers</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.marketingEmails}
                onChange={(e) => handleSettingChange('marketingEmails', e.target.checked)}
                disabled={saving}
              />
              <span className="settings-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div className="settings-section">
        <h3>SMS Notifications</h3>
        <p>Receive important alerts via text message</p>
        <div className="settings-toggles">
          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Security Alerts</h4>
              <p>Login attempts and security-related notifications</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.smsNotifications}
                onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
                disabled={saving}
              />
              <span className="settings-slider"></span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreferencesTab = () => (
    <div className="settings-tab-content">
      {error && (
        <div className="settings-error">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          {error}
        </div>
      )}

      {success && (
        <div className="settings-success">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
          {success}
        </div>
      )}

      <div className="settings-section">
        <h3>Display Preferences</h3>
        <div className="settings-form-group">
          <label>Language</label>
          <select
            value={localPreferences.language}
            onChange={(e) => handlePreferenceChange('language', e.target.value)}
            disabled={saving}
          >
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="ja">Japanese</option>
          </select>
        </div>
        <div className="settings-form-group">
          <label>Timezone</label>
          <select
            value={localPreferences.timezone}
            onChange={(e) => handlePreferenceChange('timezone', e.target.value)}
            disabled={saving}
          >
            {formattingService.getSupportedTimezones().map(tz => (
              <option key={tz.code} value={tz.code}>{tz.name}</option>
            ))}
          </select>
        </div>
        <div className="settings-form-group">
          <label>Currency Display</label>
          <select
            value={localPreferences.currency}
            onChange={(e) => handlePreferenceChange('currency', e.target.value)}
            disabled={saving}
          >
            {formattingService.getSupportedCurrencies().map(currency => (
              <option key={currency.code} value={currency.code}>
                {currency.name} ({currency.symbol})
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="settings-section">
        <h3>Account Actions</h3>
        <p>Manage your account data and settings</p>
        <div className="settings-actions">
          <button
            className="settings-btn secondary"
            onClick={handleExportData}
            disabled={saving}
          >
            {saving ? 'Exporting...' : 'Export Account Data'}
          </button>
          <button
            className="settings-btn danger"
            onClick={handleDeleteAccount}
            disabled={saving}
          >
            {saving ? 'Processing...' : 'Delete Account'}
          </button>
        </div>
        <div className="settings-actions-info">
          <p><strong>Export Account Data:</strong> Download all your account information, transactions, and investment history.</p>
          <p><strong>Delete Account:</strong> Permanently delete your account and all associated data. This action cannot be undone.</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="account-settings">
        <div className="account-settings-loading">
          <div className="loading-spinner"></div>
          <p>Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="account-settings">
      <div className="account-settings-header">
        <h1>Account Settings</h1>
        <p>Manage your security, notifications, and preferences</p>
      </div>

      <div className="account-settings-content">
        <div className="settings-tabs">
          <button 
            className={`settings-tab ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => setActiveTab('security')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
            Security
          </button>
          <button 
            className={`settings-tab ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
            Notifications
          </button>
          <button 
            className={`settings-tab ${activeTab === 'preferences' ? 'active' : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
            Preferences
          </button>
        </div>

        <div className="settings-content">
          {activeTab === 'security' && renderSecurityTab()}
          {activeTab === 'notifications' && renderNotificationsTab()}
          {activeTab === 'preferences' && renderPreferencesTab()}
        </div>
      </div>
    </div>
  );
};

export default AccountSettings;
