import { supabase } from '../lib/supabase';

class PortfolioService {
  // Get user's portfolio coins
  async getPortfolioCoins(userId) {
    try {
      const { data, error } = await supabase
        .from('portfolio_coins')
        .select('coin_id, position')
        .eq('user_id', userId)
        .order('position');

      if (error) {
        console.error('Error fetching portfolio:', error);
        return this.getDefaultPortfolio();
      }

      // If no portfolio exists, return default
      if (!data || data.length === 0) {
        return this.getDefaultPortfolio();
      }

      return data.map(item => item.coin_id);
    } catch (error) {
      console.error('Error in getPortfolioCoins:', error);
      return this.getDefaultPortfolio();
    }
  }

  // Save user's portfolio coins
  async savePortfolioCoins(userId, coinIds) {
    try {
      // First, delete existing portfolio
      const { error: deleteError } = await supabase
        .from('portfolio_coins')
        .delete()
        .eq('user_id', userId);

      if (deleteError) {
        console.error('Error deleting old portfolio:', deleteError);
        return false;
      }

      // Then insert new portfolio
      const portfolioData = coinIds.map((coinId, index) => ({
        user_id: userId,
        coin_id: coinId,
        position: index + 1
      }));

      const { error: insertError } = await supabase
        .from('portfolio_coins')
        .insert(portfolioData);

      if (insertError) {
        console.error('Error saving portfolio:', insertError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in savePortfolioCoins:', error);
      return false;
    }
  }

  // Add a coin to portfolio
  async addCoinToPortfolio(userId, coinId) {
    try {
      const currentCoins = await this.getPortfolioCoins(userId);
      
      // Check if coin already exists
      if (currentCoins.includes(coinId)) {
        return { success: false, message: 'Coin already in portfolio' };
      }

      // Check if portfolio is full
      if (currentCoins.length >= 5) {
        return { success: false, message: 'Portfolio is full (max 5 coins)' };
      }

      // Add the new coin
      const newCoins = [...currentCoins, coinId];
      const success = await this.savePortfolioCoins(userId, newCoins);

      return { 
        success, 
        message: success ? 'Coin added successfully' : 'Failed to add coin',
        coins: success ? newCoins : currentCoins
      };
    } catch (error) {
      console.error('Error in addCoinToPortfolio:', error);
      return { success: false, message: 'Error adding coin' };
    }
  }

  // Remove a coin from portfolio
  async removeCoinFromPortfolio(userId, coinId) {
    try {
      const currentCoins = await this.getPortfolioCoins(userId);
      const newCoins = currentCoins.filter(id => id !== coinId);
      
      const success = await this.savePortfolioCoins(userId, newCoins);

      return { 
        success, 
        message: success ? 'Coin removed successfully' : 'Failed to remove coin',
        coins: success ? newCoins : currentCoins
      };
    } catch (error) {
      console.error('Error in removeCoinFromPortfolio:', error);
      return { success: false, message: 'Error removing coin' };
    }
  }

  // Get default portfolio for new users or when database fails
  getDefaultPortfolio() {
    return ['bitcoin', 'ethereum', 'cardano', 'solana', 'chainlink'];
  }

  // Initialize portfolio for new user
  async initializePortfolio(userId) {
    try {
      const defaultCoins = this.getDefaultPortfolio();
      const success = await this.savePortfolioCoins(userId, defaultCoins);
      return { success, coins: defaultCoins };
    } catch (error) {
      console.error('Error initializing portfolio:', error);
      return { success: false, coins: this.getDefaultPortfolio() };
    }
  }
}

export default new PortfolioService();
