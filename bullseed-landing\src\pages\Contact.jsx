import React, { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/Contact.css';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: '📧',
      title: 'Email Support',
      value: '<EMAIL>',
      description: 'Get help with your account and investments'
    },
    {
      icon: '💬',
      title: 'SMS Support',
      value: '+1 520-226-9237',
      description: 'Text us for quick support (SMS only)'
    },
    {
      icon: '💬',
      title: 'Live Chat',
      value: 'Available 24/7',
      description: 'Instant support through our platform'
    }
  ];

  return (
    <div className="contact-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="contact-hero">
        <div className="contact-hero-container">
          <div className="contact-breadcrumb">
            <a href="/">Home</a>
            <span>/</span>
            <span>Contact</span>
          </div>
          
          <div className="contact-hero-content">
            <div className="contact-hero-badge">
              <span className="contact-hero-badge-icon">📞</span>
              Get In Touch
            </div>
            <h1 className="contact-hero-title">
              Contact <span className="highlight">BullSeed</span>
            </h1>
            <p className="contact-hero-description">
              Have questions about our investment platform? Need technical support? 
              Our professional team is here to help you 24/7.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Content */}
      <section className="contact-content">
        <div className="contact-content-container">
          <div className="contact-grid">
            {/* Contact Form */}
            <div className="contact-form-section">
              <div className="contact-form-header">
                <h2 className="contact-form-title">Send us a Message</h2>
                <p className="contact-form-description">
                  Fill out the form below and we'll get back to you within 24 hours.
                </p>
              </div>
              
              <form className="contact-form" onSubmit={handleSubmit}>
                <div className="form-group">
                  <label htmlFor="name">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="Enter your email address"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="subject">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    placeholder="What is this regarding?"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="message">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows="6"
                    placeholder="Tell us how we can help you..."
                  ></textarea>
                </div>
                
                <button type="submit" className="contact-submit-btn">
                  <span>Send Message</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                  </svg>
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="contact-info-section">
              <div className="contact-info-header">
                <h2 className="contact-info-title">Contact Information</h2>
                <p className="contact-info-description">
                  Reach out to us through any of these channels for immediate assistance.
                </p>
              </div>
              
              <div className="contact-info-grid">
                {contactInfo.map((info, index) => (
                  <div key={index} className="contact-info-card">
                    <div className="contact-info-icon">{info.icon}</div>
                    <div className="contact-info-content">
                      <h3 className="contact-info-card-title">{info.title}</h3>
                      <p className="contact-info-value">{info.value}</p>
                      <p className="contact-info-card-description">{info.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="contact-hours">
                <h3 className="contact-hours-title">Support Hours</h3>
                <div className="contact-hours-content">
                  <div className="contact-hours-item">
                    <span className="contact-hours-day">Monday - Friday</span>
                    <span className="contact-hours-time">24/7 Available</span>
                  </div>
                  <div className="contact-hours-item">
                    <span className="contact-hours-day">Weekend</span>
                    <span className="contact-hours-time">24/7 Available</span>
                  </div>
                  <div className="contact-hours-item">
                    <span className="contact-hours-day">Holidays</span>
                    <span className="contact-hours-time">Limited Support</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;
