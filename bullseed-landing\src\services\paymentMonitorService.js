import { supabase } from '../lib/supabase.js';
import depositService from './depositService.js';
import bitcoinService from './bitcoinService.js';
import ethereumService from './ethereumService.js';
import bnbService from './bnbService.js';

class PaymentMonitorService {
  constructor() {
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.checkInterval = 30000; // Check every 30 seconds
  }

  // Start monitoring for payments
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('Payment monitoring started');
    
    // Check immediately
    this.checkPendingDeposits();
    
    // Set up interval checking
    this.monitoringInterval = setInterval(() => {
      this.checkPendingDeposits();
    }, this.checkInterval);
  }

  // Stop monitoring
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('Payment monitoring stopped');
  }

  // Check all pending deposits for payments
  async checkPendingDeposits() {
    try {
      // Get all pending deposits
      const { data: pendingDeposits, error } = await supabase
        .from('crypto_deposits')
        .select('*')
        .in('status', ['pending', 'detected'])
        .lt('expires_at', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()); // Not expired

      if (error) {
        console.error('Error fetching pending deposits:', error);
        return;
      }

      if (!pendingDeposits || pendingDeposits.length === 0) {
        return;
      }

      console.log(`Checking ${pendingDeposits.length} pending deposits`);

      // Check each deposit
      for (const deposit of pendingDeposits) {
        await this.checkDepositPayment(deposit);
      }

      // Clean up expired deposits
      await this.cleanupExpiredDeposits();

    } catch (error) {
      console.error('Error in checkPendingDeposits:', error);
    }
  }

  // Check a specific deposit for payment using real blockchain APIs
  async checkDepositPayment(deposit) {
    try {
      console.log(`Checking payment for ${deposit.cryptocurrency} deposit: ${deposit.id}`);

      let recentTxs = [];

      // Get recent transactions based on cryptocurrency type
      switch (deposit.cryptocurrency) {
        case 'BTC':
          recentTxs = await bitcoinService.getTransactionsSince(new Date(deposit.created_at));
          break;
        case 'ETH':
          recentTxs = await ethereumService.getIncomingTransactions(50);
          break;
        case 'BNB':
          recentTxs = await bnbService.getIncomingTransactions(50);
          break;
        default:
          console.log(`Unsupported cryptocurrency: ${deposit.cryptocurrency}`);
          return;
      }

      // Look for a transaction that matches the expected amount
      for (const tx of recentTxs) {
        const expectedAmount = parseFloat(deposit.crypto_amount);
        let receivedAmount;
        let tolerance;

        // Get received amount and set tolerance based on cryptocurrency
        switch (deposit.cryptocurrency) {
          case 'BTC':
            receivedAmount = tx.received_amount;
            tolerance = 0.00001; // 0.00001 BTC tolerance
            break;
          case 'ETH':
            receivedAmount = tx.value;
            tolerance = 0.001; // 0.001 ETH tolerance
            break;
          case 'BNB':
            receivedAmount = tx.value;
            tolerance = 0.01; // 0.01 BNB tolerance
            break;
          default:
            continue;
        }

        const amountDifference = Math.abs(receivedAmount - expectedAmount);

        if (amountDifference <= tolerance) {
          console.log(`Found matching payment for deposit ${deposit.id}: ${receivedAmount} ${deposit.cryptocurrency}`);

          const paymentResult = {
            found: true,
            transactionHash: tx.hash,
            confirmations: tx.confirmations || 0,
            amount: receivedAmount,
            blockHeight: tx.block_height,
            receivedAt: tx.received,
            confirmedAt: tx.confirmed
          };

          await this.handlePaymentDetected(deposit, paymentResult);
          break;
        }
      }

    } catch (error) {
      console.error(`Error checking deposit ${deposit.id}:`, error);
    }
  }



  // Handle when payment is detected
  async handlePaymentDetected(deposit, paymentResult) {
    try {
      const requiredConfirmations = deposit.required_confirmations || 3;
      const currentConfirmations = paymentResult.confirmations;
      
      let newStatus = 'detected';
      let shouldUpdateBalance = false;

      // Check if we have enough confirmations
      if (currentConfirmations >= requiredConfirmations) {
        newStatus = 'confirmed';
        shouldUpdateBalance = true;
      }

      // Update deposit status
      const updates = {
        status: newStatus,
        transaction_hash: paymentResult.transactionHash,
        confirmations: currentConfirmations,
        updated_at: new Date().toISOString()
      };

      if (newStatus === 'detected' && !deposit.detected_at) {
        updates.detected_at = new Date().toISOString();
      }

      if (newStatus === 'confirmed' && !deposit.confirmed_at) {
        updates.confirmed_at = new Date().toISOString();
      }

      await depositService.updateDepositStatus(
        deposit.id,
        newStatus,
        paymentResult.transactionHash,
        currentConfirmations
      );

      // Update user balance if confirmed
      if (shouldUpdateBalance) {
        await this.processConfirmedDeposit(deposit);
      }

      console.log(`Deposit ${deposit.id} updated to ${newStatus} with ${currentConfirmations} confirmations`);

    } catch (error) {
      console.error(`Error handling payment detection for deposit ${deposit.id}:`, error);
    }
  }

  // Process confirmed deposit and update user balance
  async processConfirmedDeposit(deposit) {
    try {
      // Get current user balance and update it
      const { data: userData, error: fetchError } = await supabase
        .from('users')
        .select('balance')
        .eq('id', deposit.user_id)
        .single();

      if (fetchError) {
        console.error('Error fetching user balance:', fetchError);
        return false;
      }

      const currentBalance = userData?.balance || 0;
      const newBalance = currentBalance + deposit.amount_usd;

      // Update user balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', deposit.user_id);

      if (balanceError) {
        console.error('Error updating user balance:', balanceError);
        return false;
      }

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: deposit.user_id,
          type: 'deposit',
          amount: deposit.amount_usd,
          status: 'completed',
          description: `Crypto deposit - ${deposit.crypto_amount} ${deposit.cryptocurrency}`,
          created_at: new Date().toISOString()
        });

      if (transactionError) {
        console.error('Error creating transaction record:', transactionError);
        // Don't return false here as balance was already updated
      }

      console.log(`Processed confirmed deposit: $${deposit.amount_usd} for user ${deposit.user_id}`);
      return true;

    } catch (error) {
      console.error('Error processing confirmed deposit:', error);
      return false;
    }
  }

  // Clean up expired deposits
  async cleanupExpiredDeposits() {
    try {
      const { data: expiredDeposits, error } = await supabase
        .from('crypto_deposits')
        .update({ 
          status: 'expired',
          updated_at: new Date().toISOString()
        })
        .eq('status', 'pending')
        .lt('expires_at', new Date().toISOString())
        .select();

      if (error) {
        console.error('Error cleaning up expired deposits:', error);
        return;
      }

      if (expiredDeposits && expiredDeposits.length > 0) {
        console.log(`Marked ${expiredDeposits.length} deposits as expired`);
      }

    } catch (error) {
      console.error('Error in cleanupExpiredDeposits:', error);
    }
  }

  // Get monitoring status
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      checkInterval: this.checkInterval
    };
  }

  // Real blockchain API integration methods - Now implemented!

  // Check Bitcoin transaction
  async checkBitcoinTransaction(address, expectedAmount) {
    try {
      const transactions = await bitcoinService.getIncomingTransactions(50);
      return transactions.find(tx => Math.abs(tx.received_amount - expectedAmount) <= 0.00001);
    } catch (error) {
      console.error('Error checking Bitcoin transaction:', error);
      return null;
    }
  }

  // Check Ethereum transaction
  async checkEthereumTransaction(address, expectedAmount) {
    try {
      const transactions = await ethereumService.getIncomingTransactions(50);
      return transactions.find(tx => Math.abs(tx.value - expectedAmount) <= 0.001);
    } catch (error) {
      console.error('Error checking Ethereum transaction:', error);
      return null;
    }
  }

  // Check BNB transaction
  async checkBnbTransaction(address, expectedAmount) {
    try {
      const transactions = await bnbService.getIncomingTransactions(50);
      return transactions.find(tx => Math.abs(tx.value - expectedAmount) <= 0.01);
    } catch (error) {
      console.error('Error checking BNB transaction:', error);
      return null;
    }
  }
}

export default new PaymentMonitorService();
