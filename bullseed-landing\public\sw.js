// Empty service worker to replace any cached PWA service worker
// This will effectively disable any PWA functionality

self.addEventListener('install', (event) => {
  console.log('Empty service worker installed');
  // Skip waiting to immediately activate
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Empty service worker activated');
  
  // Clear all caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          console.log('Deleting cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      // Take control of all clients immediately
      return self.clients.claim();
    })
  );
});

// Don't intercept any fetch requests - let them go to network
self.addEventListener('fetch', (event) => {
  // Do nothing - let all requests go to network
  return;
});
