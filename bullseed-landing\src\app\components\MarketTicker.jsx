import React from 'react';
import cryptoService from '../../services/cryptoService';

const MarketTicker = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="market-ticker">
        <div className="market-ticker-loading">
          <div className="loading-spinner"></div>
          <span>Loading market data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="market-ticker">
      <div className="market-ticker-header">
        <h3>Live Market Data</h3>
        <div className="market-ticker-powered">
          <span>Powered by TradingView</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <rect x="7" y="8" width="10" height="8" rx="1" ry="1"/>
          </svg>
        </div>
      </div>

      <div className="market-ticker-scroll">
        <div className="market-ticker-items">
          {data.map((item, index) => (
            <div key={index} className="market-ticker-item">
              <div className="market-ticker-symbol">
                <span className="ticker-symbol">{item.symbol.split('/')[0]}</span>
                <span className="ticker-pair">{item.symbol}</span>
              </div>
              
              <div className="market-ticker-price">
                <span className="ticker-price">${cryptoService.formatPrice(item.price)}</span>
                <span className={`ticker-change ${item.change >= 0 ? 'positive' : 'negative'}`}>
                  {item.change >= 0 ? '▲' : '▼'} {Math.abs(item.change).toFixed(2)}%
                </span>
              </div>
              
              <div className="market-ticker-volume">
                <span className="ticker-volume-label">Vol</span>
                <span className="ticker-volume-value">{item.volume}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="market-ticker-controls">
        <button className="ticker-control-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polygon points="5,3 19,12 5,21"/>
          </svg>
        </button>
        <button className="ticker-control-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="6" y="4" width="4" height="16"/>
            <rect x="14" y="4" width="4" height="16"/>
          </svg>
        </button>
        <button className="ticker-control-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <polyline points="10,8 16,12 10,16"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default MarketTicker;
