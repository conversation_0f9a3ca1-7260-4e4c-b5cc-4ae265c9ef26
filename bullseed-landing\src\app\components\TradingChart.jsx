import React, { useEffect, useRef } from 'react';

const TradingChart = ({ timeframe = '1D', coinId = 'bitcoin' }) => {
  const containerRef = useRef(null);

  // Map coin IDs to TradingView symbols
  const getCoinSymbol = (coinId) => {
    const symbolMap = {
      'bitcoin': 'BITSTAMP:BTCUSD',
      'ethereum': 'BITSTAMP:ETHUSD',
      'cardano': 'BINANCE:ADAUSD',
      'solana': 'BINANCE:SOLUSD',
      'chainlink': 'BINANCE:LINKUSD',
      'polkadot': 'BINANCE:DOTUSD',
      'polygon': 'BINANCE:MATICUSD',
      'avalanche-2': 'BINANCE:AVAXUSD'
    };
    return symbolMap[coinId] || 'BITSTAMP:BTCUSD';
  };

  useEffect(() => {
    // Clear any existing content first
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }

    // Map timeframe to TradingView intervals
    const intervalMap = {
      '1D': '1D',
      '5D': '1D',
      '1M': '1D',
      '3M': '1W',
      '6M': '1W',
      'YTD': '1W',
      '1Y': '1M',
      'All': '1M'
    };

    // Load TradingView widget script
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js';
    script.type = 'text/javascript';
    script.async = true;
    script.innerHTML = JSON.stringify({
      "autosize": true,
      "symbol": getCoinSymbol(coinId),
      "interval": intervalMap[timeframe] || "1D",
      "timezone": "Etc/UTC",
      "theme": "dark",
      "style": "1",
      "locale": "en",
      "enable_publishing": false,
      "backgroundColor": "rgba(19, 23, 34, 1)",
      "gridColor": "rgba(42, 46, 57, 0.5)",
      "hide_top_toolbar": false,
      "hide_legend": false,
      "save_image": false,
      "calendar": false,
      "hide_volume": false,
      "support_host": "https://www.tradingview.com",
      "toolbar_bg": "#131722",
      "studies_overrides": {},
      "overrides": {
        "paneProperties.background": "#131722",
        "paneProperties.vertGridProperties.color": "rgba(42, 46, 57, 0.5)",
        "paneProperties.horzGridProperties.color": "rgba(42, 46, 57, 0.5)",
        "symbolWatermarkProperties.transparency": 90,
        "scalesProperties.textColor": "#787B86",
        "mainSeriesProperties.candleStyle.upColor": "#26a69a",
        "mainSeriesProperties.candleStyle.downColor": "#ef5350",
        "mainSeriesProperties.candleStyle.drawWick": true,
        "mainSeriesProperties.candleStyle.drawBorder": true,
        "mainSeriesProperties.candleStyle.borderColor": "#378658",
        "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
        "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
        "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
        "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350",
        "volumePaneSize": "medium"
      }
    });

    if (containerRef.current) {
      containerRef.current.appendChild(script);
    }

    return () => {
      // Clean up by clearing all content in the container
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, [timeframe, coinId]); // Re-run when timeframe or coinId changes

  return (
    <div className="trading-chart">
      <div className="trading-chart-container">
        <div
          className="tradingview-widget-container"
          ref={containerRef}
          style={{ height: '380px', width: '100%' }}
        >
        </div>
      </div>
    </div>
  );
};

export default TradingChart;
