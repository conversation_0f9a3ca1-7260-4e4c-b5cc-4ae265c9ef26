// Localization service for BullSeed app
class LocalizationService {
  constructor() {
    this.translations = {
      en: {
        // Navigation
        dashboard: 'Dashboard',
        deposit: 'Deposit',
        withdraw: 'Withdraw',
        invest: 'Invest',
        history: 'History',
        profile: 'Profile',
        settings: 'Settings',
        
        // Dashboard
        totalBalance: 'Total Balance',
        availableBalance: 'Available Balance',
        totalEarned: 'Total Earned',
        totalInvested: 'Total Invested',
        portfolioGrowth: 'Portfolio Growth',
        recentTransactions: 'Recent Transactions',
        marketOverview: 'Market Overview',
        
        // Transactions
        amount: 'Amount',
        status: 'Status',
        date: 'Date',
        type: 'Type',
        pending: 'Pending',
        completed: 'Completed',
        failed: 'Failed',
        
        // Investment Plans
        standard: 'Standard',
        promo: 'Promo',
        premium: 'Premium',
        goldMining: 'Gold Mining',
        bullseedApi: 'BullSeed API',
        
        // Common
        loading: 'Loading...',
        save: 'Save',
        cancel: 'Cancel',
        confirm: 'Confirm',
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info'
      },
      es: {
        // Navigation
        dashboard: 'Panel de Control',
        deposit: 'Depósito',
        withdraw: 'Retirar',
        invest: 'Invertir',
        history: 'Historial',
        profile: 'Perfil',
        settings: 'Configuración',
        
        // Dashboard
        totalBalance: 'Saldo Total',
        availableBalance: 'Saldo Disponible',
        totalEarned: 'Total Ganado',
        totalInvested: 'Total Invertido',
        portfolioGrowth: 'Crecimiento del Portafolio',
        recentTransactions: 'Transacciones Recientes',
        marketOverview: 'Resumen del Mercado',
        
        // Transactions
        amount: 'Cantidad',
        status: 'Estado',
        date: 'Fecha',
        type: 'Tipo',
        pending: 'Pendiente',
        completed: 'Completado',
        failed: 'Fallido',
        
        // Investment Plans
        standard: 'Estándar',
        promo: 'Promoción',
        premium: 'Premium',
        goldMining: 'Minería de Oro',
        bullseedApi: 'API BullSeed',
        
        // Common
        loading: 'Cargando...',
        save: 'Guardar',
        cancel: 'Cancelar',
        confirm: 'Confirmar',
        success: 'Éxito',
        error: 'Error',
        warning: 'Advertencia',
        info: 'Información'
      },
      fr: {
        // Navigation
        dashboard: 'Tableau de Bord',
        deposit: 'Dépôt',
        withdraw: 'Retirer',
        invest: 'Investir',
        history: 'Historique',
        profile: 'Profil',
        settings: 'Paramètres',
        
        // Dashboard
        totalBalance: 'Solde Total',
        availableBalance: 'Solde Disponible',
        totalEarned: 'Total Gagné',
        totalInvested: 'Total Investi',
        portfolioGrowth: 'Croissance du Portefeuille',
        recentTransactions: 'Transactions Récentes',
        marketOverview: 'Aperçu du Marché',
        
        // Transactions
        amount: 'Montant',
        status: 'Statut',
        date: 'Date',
        type: 'Type',
        pending: 'En Attente',
        completed: 'Terminé',
        failed: 'Échoué',
        
        // Investment Plans
        standard: 'Standard',
        promo: 'Promotion',
        premium: 'Premium',
        goldMining: 'Extraction d\'Or',
        bullseedApi: 'API BullSeed',
        
        // Common
        loading: 'Chargement...',
        save: 'Sauvegarder',
        cancel: 'Annuler',
        confirm: 'Confirmer',
        success: 'Succès',
        error: 'Erreur',
        warning: 'Avertissement',
        info: 'Information'
      },
      de: {
        // Navigation
        dashboard: 'Dashboard',
        deposit: 'Einzahlung',
        withdraw: 'Abheben',
        invest: 'Investieren',
        history: 'Verlauf',
        profile: 'Profil',
        settings: 'Einstellungen',
        
        // Dashboard
        totalBalance: 'Gesamtsaldo',
        availableBalance: 'Verfügbarer Saldo',
        totalEarned: 'Gesamt Verdient',
        totalInvested: 'Gesamt Investiert',
        portfolioGrowth: 'Portfolio-Wachstum',
        recentTransactions: 'Letzte Transaktionen',
        marketOverview: 'Marktübersicht',
        
        // Transactions
        amount: 'Betrag',
        status: 'Status',
        date: 'Datum',
        type: 'Typ',
        pending: 'Ausstehend',
        completed: 'Abgeschlossen',
        failed: 'Fehlgeschlagen',
        
        // Investment Plans
        standard: 'Standard',
        promo: 'Aktion',
        premium: 'Premium',
        goldMining: 'Gold-Mining',
        bullseedApi: 'BullSeed API',
        
        // Common
        loading: 'Laden...',
        save: 'Speichern',
        cancel: 'Abbrechen',
        confirm: 'Bestätigen',
        success: 'Erfolg',
        error: 'Fehler',
        warning: 'Warnung',
        info: 'Information'
      },
      ja: {
        // Navigation
        dashboard: 'ダッシュボード',
        deposit: '入金',
        withdraw: '出金',
        invest: '投資',
        history: '履歴',
        profile: 'プロフィール',
        settings: '設定',
        
        // Dashboard
        totalBalance: '総残高',
        availableBalance: '利用可能残高',
        totalEarned: '総獲得額',
        totalInvested: '総投資額',
        portfolioGrowth: 'ポートフォリオ成長',
        recentTransactions: '最近の取引',
        marketOverview: '市場概要',
        
        // Transactions
        amount: '金額',
        status: 'ステータス',
        date: '日付',
        type: 'タイプ',
        pending: '保留中',
        completed: '完了',
        failed: '失敗',
        
        // Investment Plans
        standard: 'スタンダード',
        promo: 'プロモ',
        premium: 'プレミアム',
        goldMining: 'ゴールドマイニング',
        bullseedApi: 'BullSeed API',
        
        // Common
        loading: '読み込み中...',
        save: '保存',
        cancel: 'キャンセル',
        confirm: '確認',
        success: '成功',
        error: 'エラー',
        warning: '警告',
        info: '情報'
      }
    };
  }

  // Get translation for a key
  t(key, language = 'en') {
    const translations = this.translations[language] || this.translations.en;
    return translations[key] || key;
  }

  // Get all translations for a language
  getTranslations(language = 'en') {
    return this.translations[language] || this.translations.en;
  }

  // Check if language is supported
  isLanguageSupported(language) {
    return Object.keys(this.translations).includes(language);
  }

  // Get supported languages
  getSupportedLanguages() {
    return [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'ja', name: '日本語' }
    ];
  }
}

export const localizationService = new LocalizationService();
