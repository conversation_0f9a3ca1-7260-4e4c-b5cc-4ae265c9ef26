.active-investments-page {
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
  min-height: 100vh;
  background: #0a0a0a;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.page-header {
  margin-bottom: 32px;
  text-align: left;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
  letter-spacing: -0.02em;
}

.page-header p {
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
  margin: 0;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
}

.error-container:hover {
  border-color: #2a2a2a;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container h3 {
  color: #ffffff;
  font-size: 20px;
  margin-bottom: 8px;
}

.error-container p {
  color: #6b7280;
  margin-bottom: 24px;
}

.retry-btn {
  background: #10b981;
  color: #ffffff;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* No Investments State */
.no-investments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
  text-align: center;
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 60px 20px;
}

.no-investments:hover {
  border-color: #2a2a2a;
}

.no-investments-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.7;
  color: #10b981;
}

.no-investments h3 {
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.no-investments p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin-bottom: 24px;
  max-width: 400px;
  line-height: 1.5;
}

.start-investing-btn {
  background: #22c55e;
  color: #ffffff;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-block;
}

.start-investing-btn:hover {
  background: #16a34a;
  transform: translateY(-1px);
}

/* Investments Container */
.investments-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Summary Cards */
.investments-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

@media (min-width: 768px) {
  .investments-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}

.summary-card {
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.summary-card:hover {
  border-color: #2a2a2a;
}

.summary-card h3 {
  color: #6b7280;
  font-size: 11px;
  font-weight: 500;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.02em;
  margin-bottom: 4px;
}

.summary-value.earned {
  color: #22c55e;
}

.summary-value.expected {
  color: #3b82f6;
}

/* Investment Detail Cards */
.investments-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (min-width: 768px) {
  .investments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
  }
}

.investment-detail-card {
  background: #111111;
  border: 1px solid #1f1f1f;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.investment-detail-card:hover {
  border-color: #2a2a2a;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #1f1f1f;
}

.plan-info h3 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.plan-details {
  display: flex;
  gap: 6px;
}

.daily-return,
.total-return {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.investment-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite;
}

.status-text {
  color: #22c55e;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

/* Card Body */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.investment-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.metric-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.metric-value.earned {
  color: #22c55e;
}

.metric-value.expected {
  color: #3b82f6;
}

.metric-value.remaining {
  color: #fbbf24;
}

/* Progress Section */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
}

.progress-percentage {
  color: #22c55e;
  font-size: 14px;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4aa, #22c55e);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Timeline */
.investment-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #1f1f1f;
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
}

.timeline-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .active-investments-page {
    padding: 16px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .investment-detail-card {
    padding: 20px;
  }

  .metric-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .active-investments-page {
    padding: 12px;
  }

  .summary-value {
    font-size: 20px;
  }

  .page-header h1 {
    font-size: 20px;
  }
}
